<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/my"></ion-back-button>
        </ion-buttons>
        <ion-title>数据诊断工具</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="toggleMonitoring">
            {{ isMonitoring ? '停止监控' : '开始监控' }}
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    
    <ion-content>
      <ion-card>
        <ion-card-header>
          <ion-card-title>监控状态</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label>
              <h3>监控状态: {{ isMonitoring ? '✅ 运行中' : '❌ 已停止' }}</h3>
              <p>总记录数: {{ statistics.total }}</p>
              <p>有效记录: {{ statistics.valid }} ({{ statistics.validRate }})</p>
              <p>无效记录: {{ statistics.invalid }}</p>
            </ion-label>
          </ion-item>
        </ion-card-content>
      </ion-card>
      
      <ion-card>
        <ion-card-header>
          <ion-card-title>快速测试</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-button expand="block" @click="runDataAnalysis">
            分析用户数据差异
          </ion-button>
          <ion-button expand="block" @click="testCurrentSettings">
            测试当前设置数据
          </ion-button>
          <ion-button expand="block" @click="exportDiagnosticLogs">
            导出诊断日志
          </ion-button>
        </ion-card-content>
      </ion-card>
      
      <ion-card>
        <ion-card-header>
          <ion-card-title>实时数据流</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div v-if="recentLogs.length === 0" class="no-data">
            暂无数据，请开始监控
          </div>
          <ion-item v-for="log in recentLogs" :key="log.timestamp">
            <ion-label>
              <h3>{{ log.source }} - {{ log.action }}</h3>
              <p>{{ log.hexString }}</p>
              <p>
                <ion-badge :color="log.isValid ? 'success' : 'danger'">
                  {{ log.isValid ? '有效' : '无效' }}
                </ion-badge>
                <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
              </p>
            </ion-label>
          </ion-item>
        </ion-card-content>
      </ion-card>
      
      <ion-card>
        <ion-card-header>
          <ion-card-title>数据分析结果</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div v-if="analysisResult" class="analysis-result">
            <h4>串口 vs Web 数据对比</h4>
            <p>{{ analysisResult.serialVsWeb.summary }}</p>
            <div v-if="!analysisResult.serialVsWeb.isMatch">
              <h5>差异详情:</h5>
              <ion-item v-for="diff in analysisResult.serialVsWeb.differences" :key="diff.position">
                <ion-label>
                  <h6>位置 {{ diff.position }} ({{ diff.description }})</h6>
                  <p>期望: {{ diff.expected }}, 实际: {{ diff.actual }}</p>
                </ion-label>
              </ion-item>
            </div>
          </div>
          <div v-else class="no-analysis">
            点击"分析用户数据差异"查看结果
          </div>
        </ion-card-content>
      </ion-card>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonBadge
} from '@ionic/vue';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { globalDiagnostics } from '@/utils/runtimeDiagnostics';
import { analyzeUserData } from '@/utils/dataAnalysisTest';
import { useBluetoothManager } from '@/composables/useBluetoothManager';

// 🔧 使用新的响应式架构替换旧的实例化方式
const { writeData } = useBluetoothManager();
const isMonitoring = computed(() => globalDiagnostics.isMonitoring.value);
const diagnosticLogs = computed(() => globalDiagnostics.diagnosticLogs.value);
const statistics = computed(() => globalDiagnostics.getStatistics());

// 最近的10条日志
const recentLogs = computed(() => {
  return diagnosticLogs.value.slice(-10).reverse();
});

const analysisResult = ref<any>(null);

const toggleMonitoring = () => {
  if (isMonitoring.value) {
    globalDiagnostics.stopMonitoring();
  } else {
    globalDiagnostics.startMonitoring();
  }
};

const runDataAnalysis = () => {
  try {
    analysisResult.value = analyzeUserData();
  } catch (error) {
    console.error('数据分析失败:', error);
  }
};

const testCurrentSettings = () => {
  if (writeData.value) {
    globalDiagnostics.logDiagnostic('DataDiagnosticPage', 'testCurrentSettings', writeData.value, '手动测试当前设置');
  }
};

const exportDiagnosticLogs = () => {
  const exportData = globalDiagnostics.exportLogs();
  
  // 在实际应用中，这里可以下载文件或复制到剪贴板
  console.log('诊断日志已导出到控制台');
  
  // 简单的复制到剪贴板（如果支持）
  if (navigator.clipboard) {
    navigator.clipboard.writeText(exportData).then(() => {
      console.log('诊断日志已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
    });
  }
};

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};

onMounted(() => {
  console.log('数据诊断页面已加载');
});

onUnmounted(() => {
  // 页面卸载时不自动停止监控，让用户手动控制
});
</script>

<style scoped>
.no-data, .no-analysis {
  text-align: center;
  color: #666;
  padding: 20px;
}

.analysis-result {
  margin-top: 10px;
}

.timestamp {
  font-size: 0.8em;
  color: #666;
  margin-left: 10px;
}

ion-badge {
  margin-right: 10px;
}
</style>