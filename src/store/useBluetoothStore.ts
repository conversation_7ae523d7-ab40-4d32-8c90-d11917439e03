import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { isPlatform } from '@ionic/vue'
import { WriteData } from '@/const/ble.const'

/**
 * 蓝牙状态管理 Store
 * 使用 Vue 3 最佳实践：响应式状态管理
 */
export const useBluetoothStore = defineStore('bluetooth', () => {
  // === 核心状态 ===
  const writeData = ref([...WriteData])
  const currentSendData = ref([...WriteData])
  const isServiceRunning = ref(false)
  const isNativeSending = ref(false)
  const lastUpdateTime = ref(0)
  
  // === 平台检测 ===
  const isAndroid = computed(() => isPlatform('android'))
  const currentMethod = computed(() => isAndroid.value ? 'native' : 'webview')
  
  // === 状态计算 ===
  const isBluetoothActive = computed(() => {
    return isAndroid.value ? isNativeSending.value : isServiceRunning.value
  })
  
  // === 数据更新方法 ===
  const updateWriteData = (newData: number[]) => {
    writeData.value = [...newData]
    lastUpdateTime.value = Date.now()
    console.log('📊 writeData已更新:', writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
  }
  
  const updateSendData = () => {
    currentSendData.value = [...writeData.value]
    console.log('📤 发送数据已更新:', currentSendData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
  }
  
  // === 服务状态管理 ===
  const setServiceRunning = (running: boolean) => {
    if (isAndroid.value) {
      isNativeSending.value = running
    } else {
      isServiceRunning.value = running
    }
    console.log(`📱 ${currentMethod.value} 服务状态:`, running)
  }
  
  // === 重置方法 ===
  const resetState = () => {
    writeData.value = [...WriteData]
    currentSendData.value = [...WriteData]
    isServiceRunning.value = false
    isNativeSending.value = false
    lastUpdateTime.value = 0
    console.log('🔄 蓝牙状态已重置')
  }
  
  return {
    // 状态
    writeData: readonly(writeData),
    currentSendData: readonly(currentSendData),
    isServiceRunning: readonly(isServiceRunning),
    isNativeSending: readonly(isNativeSending),
    lastUpdateTime: readonly(lastUpdateTime),
    
    // 计算属性
    isAndroid,
    currentMethod,
    isBluetoothActive,
    
    // 方法
    updateWriteData,
    updateSendData,
    setServiceRunning,
    resetState
  }
})
