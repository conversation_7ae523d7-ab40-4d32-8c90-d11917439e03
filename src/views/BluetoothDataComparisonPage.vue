<template>
  <ion-page>
    <ion-header :translucent="true">
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button></ion-back-button>
        </ion-buttons>
        <ion-title>蓝牙数据分析验证</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="clearResults">
            <ion-icon :icon="refreshOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">蓝牙数据分析验证</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="container">
        <!-- 测试说明 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="informationCircleOutline" style="margin-right: 8px;"></ion-icon>
              蓝牙数据分析验证说明
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <p>此工具用于分析和验证发送的蓝牙数据是否正确，类似于执行测试用例。</p>
            <p>🔍 功能包括：数据格式验证、协议一致性检查、校验和验证、参数映射验证等。</p>
            <p>⚠️ 需要开启蓝牙发送服务才能进行数据分析。</p>
          </ion-card-content>
        </ion-card>

        <!-- 数据分析状态 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>数据分析状态</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>实时监控</h3>
                <p :style="{ color: isListening ? 'var(--ion-color-success)' : 'var(--ion-color-medium)' }">
                  {{ isListening ? '正在实时监控数据变化' : '未启动实时监控' }}
                </p>
              </ion-label>
              <ion-button
                :color="isListening ? 'danger' : 'primary'"
                @click="toggleListener"
                slot="end"
              >
                {{ isListening ? '停止监控' : '开始监控' }}
              </ion-button>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>蓝牙发送状态</h3>
                <p :style="{ color: bluetoothStats.isConnected ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                  {{ bluetoothStats.isConnected ? '蓝牙发送中' : '蓝牙未连接' }}
                </p>
                <p v-if="bluetoothStats.isConnected">
                  已发送: {{ bluetoothStats.totalSent }}, 错误: {{ bluetoothStats.errorCount }}
                </p>
              </ion-label>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>最后收到导航数据</h3>
                <p>{{ lastNavigationData ? formatTime(lastNavigationData.timestamp) : '无' }}</p>
              </ion-label>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>镜像状态</h3>
                <p :style="{ color: mirrorState.enabled ? 'var(--ion-color-success)' : 'var(--ion-color-medium)' }">
                  {{ mirrorState.enabled ? '镜像已开启' : '镜像已关闭' }}
                </p>
                <p v-if="mirrorState.lastUpdate">最后更新: {{ formatTime(mirrorState.lastUpdate) }}</p>
              </ion-label>
              <ion-button 
                @click="refreshMirrorState"
                slot="end"
                fill="clear"
              >
                <ion-icon :icon="refreshOutline"></ion-icon>
              </ion-button>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 数据验证测试 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="checkmarkCircleOutline" style="margin-right: 8px;"></ion-icon>
              数据验证测试套件
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-select v-model="testData.type" placeholder="选择操作类型">
                <ion-select-option value="turn">转弯 (turn)</ion-select-option>
                <ion-select-option value="continue">直行 (continue)</ion-select-option>
                <ion-select-option value="arrive">到达 (arrive)</ion-select-option>
                <ion-select-option value="depart">出发 (depart)</ion-select-option>
              </ion-select>
              <ion-label slot="start">操作类型</ion-label>
            </ion-item>

            <ion-item>
              <ion-select v-model="testData.modifier" placeholder="选择方向修饰符">
                <ion-select-option value="left">左转 (left)</ion-select-option>
                <ion-select-option value="right">右转 (right)</ion-select-option>
                <ion-select-option value="straight">直行 (straight)</ion-select-option>
                <ion-select-option value="slight left">稍左转 (slight left)</ion-select-option>
                <ion-select-option value="slight right">稍右转 (slight right)</ion-select-option>
                <ion-select-option value="sharp left">急左转 (sharp left)</ion-select-option>
                <ion-select-option value="sharp right">急右转 (sharp right)</ion-select-option>
                <ion-select-option value="uturn">调头 (uturn)</ion-select-option>
              </ion-select>
              <ion-label slot="start">方向修饰符</ion-label>
            </ion-item>

            <ion-item>
              <ion-input 
                v-model.number="testData.stepDistance" 
                type="number" 
                placeholder="输入单次距离(米)"
              ></ion-input>
              <ion-label slot="start">单次距离</ion-label>
            </ion-item>

            <ion-item>
              <ion-input 
                v-model.number="testData.totalDistance" 
                type="number" 
                placeholder="输入总距离(米)"
              ></ion-input>
              <ion-label slot="start">总距离</ion-label>
            </ion-item>

            <!-- 快速验证测试 -->
            <ion-button
              expand="block"
              @click="runQuickValidation"
              :disabled="!bluetoothStats.isConnected"
              class="ion-margin-top"
              color="primary"
            >
              <ion-icon :icon="checkmarkCircleOutline" slot="start"></ion-icon>
              快速数据验证
            </ion-button>

            <!-- 完整测试套件 -->
            <ion-button
              expand="block"
              @click="runFullTestSuite"
              :disabled="!bluetoothStats.isConnected"
              color="secondary"
              class="ion-margin-top"
            >
              <ion-icon :icon="playOutline" slot="start"></ion-icon>
              完整测试套件
            </ion-button>

            <!-- 参数验证测试 -->
            <ion-button
              expand="block"
              @click="runParameterValidation"
              :disabled="!canPerformTest || !bluetoothStats.isConnected"
              color="tertiary"
              class="ion-margin-top"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              参数映射验证
            </ion-button>

            <!-- 协议一致性测试 -->
            <ion-button
              expand="block"
              @click="runProtocolConsistencyTest"
              color="warning"
              class="ion-margin-top"
            >
              <ion-icon :icon="informationCircleOutline" slot="start"></ion-icon>
              协议一致性测试
            </ion-button>

            <!-- 🔧 新增：数据反解析功能 -->
            <ion-button
              expand="block"
              @click="runDataParsingTest"
              color="success"
              class="ion-margin-top"
            >
              <ion-icon :icon="codeSlashOutline" slot="start"></ion-icon>
              数据反解析
            </ion-button>

            <!-- 🔧 新增：设置测试导航数据 -->
            <ion-button
              expand="block"
              @click="setTestNavigationData"
              color="medium"
              class="ion-margin-top"
            >
              <ion-icon :icon="navigateOutline" slot="start"></ion-icon>
              设置测试导航数据
            </ion-button>

            <!-- 数据修复测试 -->
            <ion-button
              expand="block"
              @click="runDataRepairTest"
              color="danger"
              class="ion-margin-top"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              数据修复测试
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- 🔧 新增：数据解析结果显示 -->
        <ion-card v-if="parseResult">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="codeSlashOutline" class="ion-margin-end"></ion-icon>
              数据解析结果
            </ion-card-title>
            <ion-card-subtitle>{{ parseResult.dataSource }} - {{ parseResult.timestamp }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <!-- 原始数据 -->
            <div class="parse-section">
              <h3>原始数据</h3>
              <div class="data-hex">
                {{ parseResult.rawDataHex }}
              </div>
            </div>

            <!-- 协议信息 -->
            <div class="parse-section">
              <h3>协议信息</h3>
              <ion-list>
                <ion-item>
                  <ion-label>
                    <h3>协议尾</h3>
                    <p>0x{{ parseResult.protocol.endByte.toString(16).toUpperCase() }}</p>
                  </ion-label>
                  <ion-icon :icon="parseResult.protocol.endByteValid ? checkmarkCircleOutline : closeCircleOutline"
                           :color="parseResult.protocol.endByteValid ? 'success' : 'danger'" slot="end"></ion-icon>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>校验和5</h3>
                    <p>0x{{ parseResult.protocol.checksum5.actual.toString(16).toUpperCase() }}
                       (期望: 0x{{ parseResult.protocol.checksum5.calculated.toString(16).toUpperCase() }})</p>
                  </ion-label>
                  <ion-icon :icon="parseResult.protocol.checksum5.isValid ? checkmarkCircleOutline : closeCircleOutline"
                           :color="parseResult.protocol.checksum5.isValid ? 'success' : 'danger'" slot="end"></ion-icon>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>校验和16</h3>
                    <p>0x{{ parseResult.protocol.checksum16.actual.toString(16).toUpperCase() }}
                       (期望: 0x{{ parseResult.protocol.checksum16.calculated.toString(16).toUpperCase() }})</p>
                  </ion-label>
                  <ion-icon :icon="parseResult.protocol.checksum16.isValid ? checkmarkCircleOutline : closeCircleOutline"
                           :color="parseResult.protocol.checksum16.isValid ? 'success' : 'danger'" slot="end"></ion-icon>
                </ion-item>
              </ion-list>
            </div>

            <!-- 设置数据 -->
            <div class="parse-section">
              <h3>设置数据</h3>
              <ion-list>
                <ion-item>
                  <ion-label>
                    <h3>档位和灯光</h3>
                    <p>{{ parseResult.settings.gearAndLight.gearName }}, 灯光: {{ parseResult.settings.gearAndLight.lightOn ? '开启' : '关闭' }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>限速和轮径</h3>
                    <p>{{ parseResult.settings.speedAndDimension.speed }}km/h, {{ parseResult.settings.speedAndDimension.dimensionName }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>P参数</h3>
                    <p>P5: {{ parseResult.settings.p5 }}, P1: {{ parseResult.settings.p1 }}, P2: {{ parseResult.settings.combinedParams.p2 }},
                       P3: {{ parseResult.settings.combinedParams.p3 }}, P4: {{ parseResult.settings.combinedParams.p4 }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>C参数</h3>
                    <p>C1: {{ parseResult.settings.c1c2.c1 }}, C2: {{ parseResult.settings.c1c2.c2 }},
                       C5: {{ parseResult.settings.c5c14.c5 }}, C14: {{ parseResult.settings.c5c14.c14 }}, C13: {{ parseResult.settings.c13 }}</p>
                  </ion-label>
                </ion-item>
              </ion-list>
            </div>

            <!-- 导航数据 -->
            <div class="parse-section">
              <h3>导航数据</h3>
              <ion-list>
                <ion-item>
                  <ion-label>
                    <h3>方向和镜像</h3>
                    <p>{{ parseResult.navigation.direction.name }}, 镜像: {{ parseResult.navigation.mirror.enabled ? '开启' : '关闭' }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>距离信息</h3>
                    <p>单次: {{ parseResult.navigation.singleDistance.actualDistance }}m,
                       总计: {{ parseResult.navigation.totalDistance.actualDistance }}m</p>
                  </ion-label>
                </ion-item>
              </ion-list>
            </div>

            <!-- 警告和错误 -->
            <div v-if="parseResult.warnings.length > 0 || parseResult.errors.length > 0" class="parse-section">
              <h3>状态信息</h3>
              <ion-list>
                <ion-item v-for="(warning, index) in parseResult.warnings" :key="'warning-' + index" color="warning">
                  <ion-icon :icon="warningOutline" slot="start" color="warning"></ion-icon>
                  <ion-label>{{ warning }}</ion-label>
                </ion-item>
                <ion-item v-for="(error, index) in parseResult.errors" :key="'error-' + index" color="danger">
                  <ion-icon :icon="closeCircleOutline" slot="start" color="danger"></ion-icon>
                  <ion-label>{{ error }}</ion-label>
                </ion-item>
              </ion-list>
            </div>
          </ion-card-content>
        </ion-card>

        <!-- 验证结果 -->
        <ion-card v-if="validationResults.length > 0">
          <ion-card-header>
            <ion-card-title>
              <ion-icon
                :icon="validationSummary.allPassed ? checkmarkCircleOutline : closeCircleOutline"
                :color="validationSummary.allPassed ? 'success' : 'danger'"
                style="margin-right: 8px;"
              ></ion-icon>
              验证结果 ({{ validationSummary.passedCount }}/{{ validationSummary.totalCount }})
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-list v-for="(result, index) in validationResults" :key="index">
              <ion-item>
                <ion-icon
                  :icon="getStatusIcon(result.status)"
                  :color="getStatusColor(result.status)"
                  slot="start"
                ></ion-icon>
                <ion-label>
                  <h3>{{ result.testName }}</h3>
                  <p>{{ getTestTypeLabel(result.testType) }} - {{ result.timestamp }}</p>
                  <p :style="{ color: getStatusTextColor(result.status) }">
                    得分: {{ result.score }}/100 | 执行时间: {{ result.executionTime }}ms
                  </p>
                  <p v-if="result.recommendations.length > 0" style="color: var(--ion-color-medium);">
                    {{ result.recommendations.slice(0, 2).join(', ') }}
                  </p>
                </ion-label>
                <ion-button
                  fill="clear"
                  @click="showValidationDetails(result)"
                  slot="end"
                >
                  查看详情
                </ion-button>
              </ion-item>

              <!-- 详细验证信息 -->
              <div v-if="result.showDetails" class="validation-details">
                <ion-item>
                  <ion-label>
                    <h4>验证详情:</h4>
                    <p>测试类型: {{ getTestTypeLabel(result.testType) }}</p>
                    <p>执行时间: {{ result.executionTime }}ms</p>
                    <p>得分: {{ result.score }}/100</p>
                  </ion-label>
                </ion-item>

                <ion-item>
                  <ion-label>
                    <h4>数据格式检查:</h4>
                    <p :style="{ color: result.details.dataFormat.valid ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                      {{ result.details.dataFormat.valid ? '✅ 通过' : '❌ 失败' }}
                    </p>
                    <p v-if="result.details.dataFormat.issues.length > 0" style="color: var(--ion-color-danger);">
                      问题: {{ result.details.dataFormat.issues.join(', ') }}
                    </p>
                  </ion-label>
                </ion-item>

                <ion-item>
                  <ion-label>
                    <h4>协议一致性检查:</h4>
                    <p :style="{ color: result.details.protocolConsistency.valid ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                      {{ result.details.protocolConsistency.valid ? '✅ 通过' : '❌ 失败' }}
                    </p>
                    <p v-if="result.details.protocolConsistency.issues.length > 0" style="color: var(--ion-color-danger);">
                      问题: {{ result.details.protocolConsistency.issues.join(', ') }}
                    </p>
                  </ion-label>
                </ion-item>

                <ion-item>
                  <ion-label>
                    <h4>原始数据:</h4>
                    <div class="data-array">
                      <span
                        v-for="(byte, i) in result.rawData"
                        :key="`data-${i}`"
                        class="byte-value"
                      >
                        {{ formatByte(byte) }}
                      </span>
                    </div>
                  </ion-label>
                </ion-item>

                <ion-item v-if="result.recommendations.length > 0">
                  <ion-label>
                    <h4>建议:</h4>
                    <p v-for="(rec, index) in result.recommendations" :key="index">
                      {{ index + 1 }}. {{ rec }}
                    </p>
                  </ion-label>
                </ion-item>
              </div>
            </ion-list>
          </ion-card-content>
        </ion-card>

        <!-- 验证统计 -->
        <ion-card v-if="validationResults.length > 0">
          <ion-card-header>
            <ion-card-title>验证统计</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>测试总数: {{ validationSummary.totalCount }}</h3>
                <p>通过: {{ validationSummary.passedCount }}</p>
                <p>失败: {{ validationSummary.failedCount }}</p>
                <p>警告: {{ validationSummary.warningCount }}</p>
                <p>通过率: {{ validationSummary.passRate }}%</p>
                <p>平均得分: {{ validationSummary.avgScore }}/100</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>测试类型统计:</h3>
                <p>快速验证: {{ quickTestCount }}次</p>
                <p>完整测试: {{ fullTestCount }}次</p>
                <p>参数验证: {{ validationResults.filter(r => r.testType === 'parameter').length }}次</p>
                <p>协议测试: {{ validationResults.filter(r => r.testType === 'protocol').length }}次</p>
                <p>修复测试: {{ validationResults.filter(r => r.testType === 'repair').length }}次</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonButtons,
  IonBackButton,
  IonIcon,
  IonList,
  isPlatform
} from '@ionic/vue'
import {
  playOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  informationCircleOutline,
  refreshOutline,
  codeSlashOutline,
  navigateOutline,
  warningOutline
} from 'ionicons/icons'
import { useNavigation } from '@/hooks/useNavigation'
import { useToast } from '@/hooks/useToast'
import { useBluetoothManager } from '@/composables/useBluetoothManager'
import { CapacitorKtService } from 'capacitor-kt-service'
import {
  validateBluetoothData,
  compareBluetoothData,
  repairBluetoothData,
  generateDiagnosticReport,
  type ValidationResult as DataValidationResult,
  type ComparisonResult as ValidationComparisonResult
} from '@/utils/dataValidation'
import { parseBluetoothData, formatParsedData } from '@/utils/bluetoothDataParser'
import { runDataValidationTests, testSpecificData } from '@/utils/dataValidationTest'
import { Directions, ManeuverDirection, DirectionProtocolMap } from '@/const/directions'

// 测试数据结构
const testData = reactive({
  type: 'turn',
  modifier: 'left',
  stepDistance: 150,
  totalDistance: 1000
})

// 监听状态
const isListening = ref(false)
const lastNavigationData = ref<any>(null)
const bluetoothStats = ref({
  isConnected: false,
  totalSent: 0,
  errorCount: 0
})

// 镜像状态
const mirrorState = ref({
  enabled: false,
  lastUpdate: null as number | null
})

// 🔧 新增：解析结果数据
const parseResult = ref<any>(null)

// 验证结果结构
interface ValidationResult {
  testName: string
  timestamp: string
  testType: 'quick' | 'full' | 'parameter' | 'protocol' | 'repair'
  status: 'passed' | 'failed' | 'warning'
  score: number // 0-100 分数
  details: {
    dataFormat: { valid: boolean; issues: string[] }
    protocolConsistency: { valid: boolean; issues: string[] }
    checksumValidation: { valid: boolean; issues: string[] }
    parameterMapping: { valid: boolean; issues: string[] }
    navigationData: { valid: boolean; issues: string[] }
  }
  rawData: number[]
  recommendations: string[]
  showDetails: boolean
  executionTime: number // 执行时间(ms)
}

const validationResults = ref<ValidationResult[]>([])
const { presentToast } = useToast()

// 🔧 使用新的响应式架构获取设置数据
const bluetoothManager = useBluetoothManager()

// 监听器
let routeProgressListener: any = null

// 检查是否可以执行测试
const canPerformTest = computed(() => {
  return testData.type && 
         testData.modifier && 
         typeof testData.stepDistance === 'number' && 
         typeof testData.totalDistance === 'number'
})

// 验证统计信息
const validationSummary = computed(() => {
  const total = validationResults.value.length
  const passed = validationResults.value.filter(r => r.status === 'passed').length
  const failed = validationResults.value.filter(r => r.status === 'failed').length
  const warnings = validationResults.value.filter(r => r.status === 'warning').length
  const passRate = total > 0 ? Math.round((passed / total) * 100) : 0
  const avgScore = total > 0 ? Math.round(validationResults.value.reduce((sum, r) => sum + r.score, 0) / total) : 0

  return {
    totalCount: total,
    passedCount: passed,
    failedCount: failed,
    warningCount: warnings,
    passRate,
    avgScore,
    allPassed: failed === 0 && total > 0
  }
})

const quickTestCount = computed(() =>
  validationResults.value.filter(r => r.testType === 'quick').length
)

const fullTestCount = computed(() =>
  validationResults.value.filter(r => r.testType === 'full').length
)

// 🔧 新增：快速数据验证
const runQuickValidation = async () => {
  const startTime = performance.now()
  try {
    console.log('🚀 开始快速数据验证...')
    await presentToast('开始快速数据验证')

    // 获取当前数据
    const bluetoothManager = useBluetoothManager()
    const currentData = [...bluetoothManager.writeData.value]

    // 基础验证
    const validation = validateBluetoothData(currentData)
    const executionTime = performance.now() - startTime

    const result: ValidationResult = {
      testName: '快速数据验证',
      timestamp: new Date().toLocaleString(),
      testType: 'quick',
      status: validation.isValid ? 'passed' : 'failed',
      score: validation.isValid ? 100 : Math.max(0, 100 - validation.errors.length * 20),
      details: {
        dataFormat: { valid: validation.isValid, issues: validation.errors },
        protocolConsistency: { valid: true, issues: [] },
        checksumValidation: { valid: validation.isValid, issues: [] },
        parameterMapping: { valid: true, issues: [] },
        navigationData: { valid: true, issues: [] }
      },
      rawData: currentData,
      recommendations: validation.isValid ? ['数据格式正确'] : validation.errors,
      showDetails: false,
      executionTime: Math.round(executionTime)
    }

    validationResults.value.unshift(result)

    if (validation.isValid) {
      await presentToast('✅ 快速验证通过')
    } else {
      await presentToast(`❌ 快速验证失败: ${validation.errors.length} 个问题`)
    }

  } catch (error: any) {
    console.error('❌ 快速验证失败:', error)
    await presentToast(`快速验证失败: ${error.message}`)
  }
}

// 🔧 新增：完整测试套件
const runFullTestSuite = async () => {
  const startTime = performance.now()
  try {
    console.log('🚀 开始完整测试套件...')
    await presentToast('开始完整测试套件，请稍候...')

    // 🔧 修复：获取Native端实际发送的数据作为期望值
    const nativeResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
    if (!nativeResult.success) {
      throw new Error(`无法获取Native端数据: ${nativeResult.error || '未知错误'}`)
    }

    const nativeData = nativeResult.data
    console.log('🔍 Native端实际发送数据:', nativeData.map((b: number) => `0x${b.toString(16).toUpperCase()}`).join(' '))

    // 🔧 获取Vue端当前数据进行对比
    const bluetoothManager = useBluetoothManager()
    const vueData = [...bluetoothManager.writeData.value]
    console.log('🔍 Vue端当前数据:', vueData.map(b => `0x${b.toString(16).toUpperCase()}`).join(' '))

    // 🔧 进行数据对比分析
    let comparisonScore = 100
    let comparisonIssues: string[] = []

    const comparison = compareBluetoothData(vueData, nativeData)
    comparisonScore = comparison.analysis.overallConsistency * 100
    comparisonIssues = comparison.differences.map(d =>
      `字节${d.index}: Vue端0x${d.expected.toString(16).toUpperCase()}, Native端0x${d.actual.toString(16).toUpperCase()}`
    )

    console.log('🔍 数据对比结果:', {
      一致性得分: `${comparisonScore.toFixed(1)}%`,
      差异数量: comparison.differences.length,
      差异详情: comparisonIssues
    })

    // 🔧 综合验证：分别验证Vue端和Native端数据
    const vueValidation = validateBluetoothData(vueData)
    const nativeValidation = validateBluetoothData(nativeData)
    const executionTime = performance.now() - startTime

    // 🔧 计算综合得分
    const vueScore = vueValidation.isValid ? 25 : 0
    const nativeScore = nativeValidation.isValid ? 25 : 0
    const consistencyScore = comparisonScore * 0.5
    const overallScore = Math.round(vueScore + nativeScore + consistencyScore)

    console.log('🔍 综合评分:', {
      Vue端数据有效性: `${vueScore}/25`,
      Native端数据有效性: `${nativeScore}/25`,
      数据一致性: `${consistencyScore.toFixed(1)}/50`,
      总分: `${overallScore}/100`
    })

    const result: ValidationResult = {
      testName: '完整测试套件',
      timestamp: new Date().toLocaleString(),
      testType: 'full',
      status: overallScore >= 80 ? 'passed' : overallScore >= 60 ? 'warning' : 'failed',
      score: overallScore,
      details: {
        dataFormat: {
          valid: vueValidation.isValid && nativeValidation.isValid,
          issues: [...vueValidation.errors, ...nativeValidation.errors]
        },
        protocolConsistency: { valid: comparisonScore >= 80, issues: comparisonIssues },
        checksumValidation: {
          valid: vueValidation.isValid && nativeValidation.isValid,
          issues: []
        },
        parameterMapping: { valid: comparisonScore >= 90, issues: comparisonIssues },
        navigationData: { valid: comparisonScore >= 70, issues: comparisonIssues }
      },
      rawData: nativeData, // 🔧 使用Native端数据作为参考
      recommendations: [
        ...(vueValidation.isValid ? [] : [`Vue端数据问题: ${vueValidation.errors.join(', ')}`]),
        ...(nativeValidation.isValid ? [] : [`Native端数据问题: ${nativeValidation.errors.join(', ')}`]),
        ...(comparisonScore < 80 ? ['Vue端与Native端数据不一致，建议检查数据同步'] : []),
        ...(comparisonScore >= 95 ? ['数据一致性优秀'] : [])
      ],
      showDetails: false,
      executionTime: Math.round(executionTime)
    }

    validationResults.value.unshift(result)

    await presentToast(`完整测试完成，得分: ${overallScore}/100`)

  } catch (error: any) {
    console.error('❌ 完整测试失败:', error)
    await presentToast(`完整测试失败: ${error.message}`)
  }
}

// 🔧 新增：参数映射验证
const runParameterValidation = async () => {
  const startTime = performance.now()
  try {
    console.log('🚀 开始参数映射验证...')
    await presentToast('开始参数映射验证')

    // 使用测试数据进行参数验证
    const mockNavigationData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: testData.type,
          modifier: testData.modifier
        }
      }),
      stepDistanceRemaining: testData.stepDistance,
      distanceRemaining: testData.totalDistance
    }

    const navigation = useNavigation()
    const bluetoothManager = useBluetoothManager()
    navigation.syncWithSettingData(bluetoothManager.writeData.value)

    const calculatedData = navigation.calculateNavigationDataIndependently(
      mockNavigationData,
      createInitialWriteData(),
      false
    )

    const validation = validateBluetoothData(calculatedData)
    const executionTime = performance.now() - startTime

    const result: ValidationResult = {
      testName: `参数映射验证 (${testData.type}/${testData.modifier})`,
      timestamp: new Date().toLocaleString(),
      testType: 'parameter',
      status: validation.isValid ? 'passed' : 'failed',
      score: validation.isValid ? 100 : 60,
      details: {
        dataFormat: { valid: validation.isValid, issues: validation.errors },
        protocolConsistency: { valid: true, issues: [] },
        checksumValidation: { valid: validation.isValid, issues: [] },
        parameterMapping: { valid: validation.isValid, issues: validation.errors },
        navigationData: { valid: true, issues: [] }
      },
      rawData: calculatedData,
      recommendations: validation.isValid ?
        ['参数映射正确'] :
        ['参数映射存在问题，请检查导航数据处理逻辑'],
      showDetails: false,
      executionTime: Math.round(executionTime)
    }

    validationResults.value.unshift(result)

    if (validation.isValid) {
      await presentToast('✅ 参数映射验证通过')
    } else {
      await presentToast('❌ 参数映射验证失败')
    }

  } catch (error: any) {
    console.error('❌ 参数映射验证失败:', error)
    await presentToast(`参数映射验证失败: ${error.message}`)
  }
}

// 🔧 新增：协议一致性测试
const runProtocolConsistencyTest = async () => {
  const startTime = performance.now()
  try {
    console.log('🚀 开始协议一致性测试...')
    await presentToast('开始协议一致性测试')

    const bluetoothManager = useBluetoothManager()
    const currentData = [...bluetoothManager.writeData.value]

    // 检查协议格式
    const protocolIssues: string[] = []
    let protocolScore = 100

    // 检查起始字节
    if (currentData[0] !== 0x0F) {
      protocolIssues.push('起始字节错误')
      protocolScore -= 20
    }

    // 检查结束字节
    if (currentData[17] !== 0x0E) {
      protocolIssues.push('结束字节错误')
      protocolScore -= 20
    }

    // 检查数据长度
    if (currentData.length !== 18) {
      protocolIssues.push('数据长度错误')
      protocolScore -= 30
    }

    // 检查校验和
    let expectedChecksum = 0
    for (let i = 1; i <= 15; i++) {
      if (i !== 5) expectedChecksum ^= currentData[i]
    }
    expectedChecksum &= 0xFF

    if (currentData[16] !== expectedChecksum) {
      protocolIssues.push('校验和错误')
      protocolScore -= 30
    }

    const executionTime = performance.now() - startTime

    const result: ValidationResult = {
      testName: '协议一致性测试',
      timestamp: new Date().toLocaleString(),
      testType: 'protocol',
      status: protocolScore >= 80 ? 'passed' : protocolScore >= 60 ? 'warning' : 'failed',
      score: Math.max(0, protocolScore),
      details: {
        dataFormat: { valid: protocolScore >= 80, issues: protocolIssues },
        protocolConsistency: { valid: protocolScore >= 80, issues: protocolIssues },
        checksumValidation: { valid: currentData[16] === expectedChecksum, issues: [] },
        parameterMapping: { valid: true, issues: [] },
        navigationData: { valid: true, issues: [] }
      },
      rawData: currentData,
      recommendations: protocolScore >= 80 ?
        ['协议格式正确'] :
        protocolIssues.map(issue => `修复: ${issue}`),
      showDetails: false,
      executionTime: Math.round(executionTime)
    }

    validationResults.value.unshift(result)

    await presentToast(`协议一致性测试完成，得分: ${Math.max(0, protocolScore)}/100`)

  } catch (error: any) {
    console.error('❌ 协议一致性测试失败:', error)
    await presentToast(`协议一致性测试失败: ${error.message}`)
  }
}

// 🔧 新增：数据修复测试
const runDataRepairTest = async () => {
  const startTime = performance.now()
  try {
    console.log('🚀 开始数据修复测试...')
    await presentToast('开始数据修复测试')

    const bluetoothManager = useBluetoothManager()
    const originalData = [...bluetoothManager.writeData.value]

    // 尝试修复数据
    const { repaired: repairedData, changes } = repairBluetoothData(originalData, {
      fixChecksum: true,
      fixProtocolHeader: true,
      fixEndByte: true
    })

    const validation = validateBluetoothData(repairedData)
    const executionTime = performance.now() - startTime

    const result: ValidationResult = {
      testName: '数据修复测试',
      timestamp: new Date().toLocaleString(),
      testType: 'repair',
      status: validation.isValid ? 'passed' : 'warning',
      score: validation.isValid ? 100 : 70,
      details: {
        dataFormat: { valid: validation.isValid, issues: validation.errors },
        protocolConsistency: { valid: true, issues: [] },
        checksumValidation: { valid: validation.isValid, issues: [] },
        parameterMapping: { valid: true, issues: [] },
        navigationData: { valid: true, issues: [] }
      },
      rawData: repairedData,
      recommendations: [
        `应用了 ${changes.length} 项修复`,
        ...changes,
        ...(validation.isValid ? ['修复后数据有效'] : ['修复后仍有问题'])
      ],
      showDetails: false,
      executionTime: Math.round(executionTime)
    }

    validationResults.value.unshift(result)

    await presentToast(`数据修复测试完成，应用了 ${changes.length} 项修复`)

  } catch (error: any) {
    console.error('❌ 数据修复测试失败:', error)
    await presentToast(`数据修复测试失败: ${error.message}`)
  }
}

// 开启/关闭实时监控
const toggleListener = async () => {
  if (isListening.value) {
    await stopListener()
  } else {
    await startListener()
  }
}

// 开始实时监控数据变化
const startListener = async () => {
  try {
    console.log('🎧 开始实时监控数据变化')

    // 监听导航进度变化事件，用于实时验证
    routeProgressListener = await CapacitorKtService.addListener('onRouteProgressChange', (data: any) => {
      console.log('📊 收到导航进度数据:', data)
      lastNavigationData.value = {
        ...data,
        timestamp: Date.now()
      }

      // 执行实时数据验证
      performRealTimeValidation(data)
    })

    // 监听镜像状态变化事件
    await CapacitorKtService.addListener('onScreenMirroringChange', (data: any) => {
      console.log('🔄 镜像状态变化:', data)
      mirrorState.value = {
        enabled: data.enabled,
        lastUpdate: Date.now()
      }
    })

    isListening.value = true
    await presentToast('已开始实时监控数据变化')

    // 启动蓝牙状态更新定时器
    startBluetoothStatsUpdate()

    // 初始获取镜像状态
    await refreshMirrorState()

  } catch (error: any) {
    console.error('❌ 启动监控失败:', error)
    await presentToast(`启动监控失败: ${error.message}`)
  }
}

// 🔧 新增：实时数据验证
const performRealTimeValidation = async (navigationData: any) => {
  try {
    console.log('🔍 执行实时数据验证')

    const bluetoothManager = useBluetoothManager()
    const currentData = [...bluetoothManager.writeData.value]

    // 快速验证当前数据
    const validation = validateBluetoothData(currentData)

    if (!validation.isValid) {
      console.warn('⚠️ 实时验证发现问题:', validation.errors)
      await presentToast(`实时验证警告: ${validation.errors[0]}`)
    }

    // 可以选择自动添加到验证结果中
    // 这里暂时只在控制台输出，避免结果列表过于频繁更新

  } catch (error: any) {
    console.error('❌ 实时验证失败:', error)
  }
}

// 停止监控
const stopListener = async () => {
  try {
    if (routeProgressListener) {
      await routeProgressListener.remove()
      routeProgressListener = null
    }

    isListening.value = false
    await presentToast('已停止监控数据变化')

  } catch (error: any) {
    console.error('❌ 停止监控失败:', error)
    await presentToast(`停止监控失败: ${error.message}`)
  }
}

// 🔧 注释掉旧的对比方法，改为验证分析模式
// 这些方法已被新的验证测试套件替代

// 🔧 保留一些辅助方法供验证使用

// 🔧 移除旧的比对分析方法，使用新的验证工具代替

// 🔧 旧的手动对比方法已移除，使用新的参数验证测试代替

// 🔧 保留一些基础方法

// 定时更新蓝牙状态
let bluetoothStatsTimer: any = null

const startBluetoothStatsUpdate = () => {
  bluetoothStatsTimer = setInterval(async () => {
    try {
      const stats = await CapacitorKtService.getBluetoothSendingStats()
      bluetoothStats.value = {
        isConnected: stats.isConnected,
        totalSent: stats.totalSent,
        errorCount: stats.errorCount
      }
    } catch (error) {
      console.warn('获取蓝牙统计失败:', error)
    }
  }, 2000) // 每2秒更新一次
}

const stopBluetoothStatsUpdate = () => {
  if (bluetoothStatsTimer) {
    clearInterval(bluetoothStatsTimer)
    bluetoothStatsTimer = null
  }
}

// 刷新镜像状态
const refreshMirrorState = async () => {
  try {
    const result = await (CapacitorKtService as any).getMirrorState()
    mirrorState.value = {
      enabled: result.enabled,
      lastUpdate: Date.now()
    }
    console.log('🔄 镜像状态已刷新:', result.enabled)
  } catch (error: any) {
    console.error('❌ 刷新镜像状态失败:', error)
    await presentToast(`刷新镜像状态失败: ${error.message}`)
  }
}

// 🔧 新增：辅助方法
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'passed': return checkmarkCircleOutline
    case 'failed': return closeCircleOutline
    case 'warning': return informationCircleOutline
    default: return informationCircleOutline
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'passed': return 'success'
    case 'failed': return 'danger'
    case 'warning': return 'warning'
    default: return 'medium'
  }
}

const getStatusTextColor = (status: string) => {
  switch (status) {
    case 'passed': return 'var(--ion-color-success)'
    case 'failed': return 'var(--ion-color-danger)'
    case 'warning': return 'var(--ion-color-warning)'
    default: return 'var(--ion-color-medium)'
  }
}

const getTestTypeLabel = (testType: string) => {
  switch (testType) {
    case 'quick': return '快速验证'
    case 'full': return '完整测试'
    case 'parameter': return '参数验证'
    case 'protocol': return '协议测试'
    case 'repair': return '修复测试'
    default: return '未知测试'
  }
}

// 显示验证详情
const showValidationDetails = (result: ValidationResult) => {
  result.showDetails = !result.showDetails
}

// 清除结果
const clearResults = () => {
  validationResults.value = []
}

// 🔧 旧的数据一致性检查方法已移除，使用新的完整测试套件代替

// 🔧 旧的自动修复方法已移除，使用新的数据修复测试代替

// 格式化字节显示
const formatByte = (byte: number) => {
  return `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 🔧 移除不再需要的模拟数据生成方法

// 🔧 旧的无导航状态对比方法已移除

// 🔧 保留一些基础辅助方法

// 🔧 简化的验证测试方法
const runValidationTests = async () => {
  try {
    console.log('🧪 开始运行数据验证测试...')
    await presentToast('开始运行验证测试，请查看控制台输出')

    // 运行基础验证测试
    runDataValidationTests()

    // 如果有实际数据，也进行测试
    try {
      const bluetoothManager = useBluetoothManager()
      const vueData = [...bluetoothManager.writeData.value]

      const nativeResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
      if (nativeResult.success && nativeResult.data) {
        console.log('\n🔍 测试当前实际数据:')
        testSpecificData(vueData, nativeResult.data)
      }
    } catch (error) {
      console.warn('⚠️ 无法获取实际数据进行测试:', error)
    }

    await presentToast('✅ 验证测试完成，请查看控制台详细结果')

  } catch (error: any) {
    console.error('❌ 验证测试失败:', error)
    await presentToast(`验证测试失败: ${error.message}`)
  }
}

// 🔧 新增：数据反解析测试 (支持Web端和Native端)
const runDataParsingTest = async () => {
  try {
    console.log('🔍 开始数据反解析测试...')
    await presentToast('开始数据反解析，结果将显示在界面上...')

    // 🔧 检测当前平台
    const isAndroid = isPlatform('android')
    console.log('🔍 当前平台:', isAndroid ? 'Android (Native)' : 'Web/iOS')

    let actualSendData: number[] = []
    let dataSource = ''

    if (isAndroid) {
      // Android端：获取Native端实际发送的数据
      try {
        const nativeResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
        if (nativeResult.success) {
          actualSendData = nativeResult.data
          dataSource = 'Native端'
          console.log('🔍 Native端原始数据:', actualSendData.map((b: number) => `0x${b.toString(16).toUpperCase()}`).join(' '))
        } else {
          throw new Error(`无法获取Native端数据: ${nativeResult.error || '未知错误'}`)
        }
      } catch (error) {
        console.warn('⚠️ Native端数据获取失败，回退到Vue端数据:', error)
        // 回退到Vue端数据
        const bluetoothManager = useBluetoothManager()
        actualSendData = [...bluetoothManager.writeData.value]
        dataSource = 'Vue端 (Native端获取失败)'
      }
    } else {
      // Web/iOS端：使用Vue端数据
      const bluetoothManager = useBluetoothManager()
      actualSendData = [...bluetoothManager.writeData.value]
      dataSource = 'Vue端 (Web平台)'
      console.log('🔍 Web端原始数据:', actualSendData.map(b => `0x${b.toString(16).toUpperCase()}`).join(' '))
    }

    // 解析实际发送的数据
    const actualParsed = parseBluetoothData(actualSendData)
    console.log(`🔍 ${dataSource}解析结果:`, actualParsed)

    // 🔧 将解析结果格式化为界面显示格式
    parseResult.value = {
      dataSource: dataSource,
      timestamp: new Date().toLocaleString(),
      rawDataHex: actualSendData.map(b => `0x${b.toString(16).toUpperCase()}`).join(' '),
      protocol: {
        endByte: actualParsed.protocol.endByte,
        endByteValid: actualParsed.protocol.endByte === 0x0e,
        checksum5: actualParsed.protocol.checksum5,
        checksum16: actualParsed.protocol.checksum16,
        isValid: actualParsed.protocol.isValid
      },
      settings: actualParsed.settings,
      navigation: actualParsed.navigation,
      warnings: actualParsed.parseStatus.warnings,
      errors: actualParsed.parseStatus.errors
    }

    // 获取Vue端数据进行对比 (如果数据源不是Vue端)
    let vueParsed: any = null
    if (dataSource !== 'Vue端 (Web平台)') {
      const bluetoothManager = useBluetoothManager()
      const vueData = [...bluetoothManager.writeData.value]
      console.log('🔍 Vue端原始数据:', vueData.map(b => `0x${b.toString(16).toUpperCase()}`).join(' '))
      vueParsed = parseBluetoothData(vueData)
      console.log('🔍 Vue端解析结果:', vueParsed)
    }

    // 生成格式化报告
    console.log(`\n📋 ${dataSource}数据解析报告:`)
    console.log(formatParsedData(actualParsed))

    if (vueParsed) {
      console.log('\n📋 Vue端数据解析报告:')
      console.log(formatParsedData(vueParsed))

      // 对比关键差异 (仅在有两个数据源时)
      console.log('\n🔍 关键数据对比:')
      console.log('导航方向:', {
        [dataSource]: actualParsed.navigation.direction.name,
        Vue端: vueParsed.navigation.direction.name,
        一致: actualParsed.navigation.direction.code === vueParsed.navigation.direction.code
      })

      console.log('镜像状态:', {
        [dataSource]: actualParsed.navigation.mirror.enabled,
        Vue端: vueParsed.navigation.mirror.enabled,
        一致: actualParsed.navigation.mirror.enabled === vueParsed.navigation.mirror.enabled
      })

      console.log('单次距离:', {
        [dataSource]: `${actualParsed.navigation.singleDistance.actualDistance}m`,
        Vue端: `${vueParsed.navigation.singleDistance.actualDistance}m`,
        一致: actualParsed.navigation.singleDistance.actualDistance === vueParsed.navigation.singleDistance.actualDistance
      })

      console.log('总距离:', {
        [dataSource]: `${actualParsed.navigation.totalDistance.actualDistance}m`,
        Vue端: `${vueParsed.navigation.totalDistance.actualDistance}m`,
        一致: actualParsed.navigation.totalDistance.actualDistance === vueParsed.navigation.totalDistance.actualDistance
      })
    } else {
      // Web端单一数据源的情况
      console.log('\n🔍 解析的关键信息:')
      console.log('导航方向:', actualParsed.navigation.direction.name)
      console.log('镜像状态:', actualParsed.navigation.mirror.enabled ? '开启' : '关闭')
      console.log('单次距离:', `${actualParsed.navigation.singleDistance.actualDistance}m`)
      console.log('总距离:', `${actualParsed.navigation.totalDistance.actualDistance}m`)
      console.log('当前档位:', actualParsed.settings.gearAndLight.gearName)
      console.log('限速设置:', `${actualParsed.settings.speedAndDimension.speed}km/h`)
      console.log('轮径设置:', actualParsed.settings.speedAndDimension.dimensionName)
    }

    await presentToast('✅ 数据反解析完成，结果已显示在界面上')

  } catch (error: any) {
    console.error('❌ 数据反解析失败:', error)
    parseResult.value = null
    await presentToast(`数据反解析失败: ${error.message}`)
  }
}

// 🔧 新增：设置测试导航数据
const setTestNavigationData = async () => {
  try {
    console.log('🔧 设置测试导航数据...')
    await presentToast('设置测试导航数据...')

    const bluetoothManager = useBluetoothManager()

    // 🔧 根据用户设置的testData动态生成导航数据
    console.log('🔧 当前测试数据设置:', testData)

    // 方向映射：将用户选择的修饰符映射到协议数字
    const directionMap: { [key: string]: number } = {
      'left': 1,           // 左转
      'right': 2,          // 右转
      'straight': 3,       // 直行
      'slight left': 5,    // 向左前方行驶
      'slight right': 6,   // 向右前方行驶
      'sharp left': 1,     // 左转（急转）
      'sharp right': 2,    // 右转（急转）
      'uturn': 4          // 调头
    }

    const directionCode = directionMap[testData.modifier] || 1  // 默认左转
    const stepDistance = testData.stepDistance || 150
    const totalDistance = testData.totalDistance || 1000

    // 🔧 修复距离编码逻辑：根据距离值选择合适的规则并编码
    const encodeDistance = (distance: number) => {
      // 选择最合适的规则（尽量减少精度损失）
      if (distance % 1000 === 0 && distance <= 1023000) {
        return { rule: 3, encodedValue: distance / 1000 }  // 千位显示
      } else if (distance % 100 === 0 && distance <= 102300) {
        return { rule: 2, encodedValue: distance / 100 }   // 百位显示
      } else if (distance % 10 === 0 && distance <= 10230) {
        return { rule: 1, encodedValue: distance / 10 }    // 十位显示
      } else {
        return { rule: 0, encodedValue: distance }         // 个位显示
      }
    }

    const singleEncoded = encodeDistance(stepDistance)
    const totalEncoded = encodeDistance(totalDistance)

    // 字节12: 镜像位(位7) + 单次距离规则(位6-4) + 方向(位3-0)
    const testNav12 = 0x80 | (singleEncoded.rule << 4) | directionCode

    // 字节13: 单次距离编码值低位
    const testNav13 = singleEncoded.encodedValue & 0xFF

    // 字节14: 单次距离编码值高位(位7-6) + 总距离规则(位5-4) + 总距离编码值高位(位3-2)
    const singleHigh = (singleEncoded.encodedValue >> 8) & 0x03
    const totalHigh = (totalEncoded.encodedValue >> 8) & 0x03
    const testNav14 = (singleHigh << 6) | (totalEncoded.rule << 4) | (totalHigh << 2)

    // 字节15: 总距离编码值低位
    const testNav15 = totalEncoded.encodedValue & 0xFF

    // 更新导航数据
    bluetoothManager.updateNavigationData?.(testNav12, testNav13, testNav14, testNav15)

    console.log('🔧 测试导航数据已设置:', {
      用户设置: {
        操作类型: testData.type,
        方向修饰符: testData.modifier,
        单次距离: `${stepDistance}m`,
        总距离: `${totalDistance}m`
      },
      编码结果: {
        字节12: `0x${testNav12.toString(16).toUpperCase()} (镜像=开启, 单次规则=${singleEncoded.rule}, 方向=${directionCode})`,
        字节13: `0x${testNav13.toString(16).toUpperCase()} (单次编码值低位=${testNav13})`,
        字节14: `0x${testNav14.toString(16).toUpperCase()} (单次高位=${singleHigh}, 总距离规则=${totalEncoded.rule}, 总距离高位=${totalHigh})`,
        字节15: `0x${testNav15.toString(16).toUpperCase()} (总距离编码值低位=${testNav15})`
      },
      编码详情: {
        单次距离: `${stepDistance}m → 规则${singleEncoded.rule} → 编码值${singleEncoded.encodedValue}`,
        总距离: `${totalDistance}m → 规则${totalEncoded.rule} → 编码值${totalEncoded.encodedValue}`
      },
      预期解析结果: {
        镜像: '开启',
        方向: testData.modifier,
        单次距离: `${stepDistance}m`,
        总距离: `${totalDistance}m`
      }
    })

    await presentToast('✅ 测试导航数据设置完成')

  } catch (error: any) {
    console.error('❌ 设置测试导航数据失败:', error)
    await presentToast(`设置失败: ${error.message}`)
  }
}

// 创建初始WriteData
const createInitialWriteData = () => [
  0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
  0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [12，13，14，15，16]
  0x0e //17 截止位
]

// 组件生命周期
onMounted(async () => {
  // 初始获取蓝牙状态
  try {
    const stats = await CapacitorKtService.getBluetoothSendingStats()
    bluetoothStats.value = {
      isConnected: stats.isConnected,
      totalSent: stats.totalSent,
      errorCount: stats.errorCount
    }
  } catch (error) {
    console.warn('初始化蓝牙统计失败:', error)
  }

  // 初始获取镜像状态
  try {
    const mirrorStateResult = await CapacitorKtService.getMirrorState()
    mirrorState.value = {
      enabled: mirrorStateResult.enabled,
      lastUpdate: Date.now()
    }
  } catch (error) {
    console.warn('初始化镜像状态失败:', error)
  }
})

onUnmounted(async () => {
  await stopListener()
  stopBluetoothStatsUpdate()
})
</script>

<style scoped>
.container {
  padding: 16px;
}

.validation-details {
  background-color: var(--ion-color-light);
  margin: 8px 0;
  border-radius: 8px;
}

.data-array {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  font-family: 'Courier New', monospace;
}

.byte-value {
  padding: 2px 6px;
  background-color: var(--ion-color-medium-tint);
  border-radius: 4px;
  font-size: 12px;
  min-width: 32px;
  text-align: center;
}

.byte-diff {
  background-color: var(--ion-color-danger) !important;
  color: white;
  font-weight: bold;
}

ion-item {
  --padding-start: 16px;
}

ion-card {
  margin: 8px 0;
}

.ion-margin-top {
  margin-top: 16px;
}

/* 🔧 新增：解析结果样式 */
.parse-section {
  margin: 16px 0;
}

.parse-section h3 {
  color: var(--ion-color-primary);
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 8px;
  border-bottom: 1px solid var(--ion-color-light);
  padding-bottom: 4px;
}

.data-hex {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  background: var(--ion-color-light);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--ion-color-medium);
  word-break: break-all;
  line-height: 1.4;
}

.parse-section ion-list {
  background: transparent;
}

.parse-section ion-item {
  --background: var(--ion-color-step-50);
  margin: 4px 0;
  border-radius: 6px;
}

.parse-section ion-item h3 {
  font-size: 0.9em;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin: 0;
  border: none;
  padding: 0;
}

.parse-section ion-item p {
  font-size: 0.85em;
  color: var(--ion-color-medium-shade);
  margin: 2px 0 0 0;
}
</style>