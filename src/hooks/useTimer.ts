import { computed, ref, watch } from "vue";

export function useTimer() {
  let startTime = 0; // 🔧 修复：动态设置开始时间
  const elapsedTime = ref(0);
  const timerRunning = ref(false);
  let intervalId: number | NodeJS.Timer | undefined; // 🔧 修复：更好的类型定义

  const toggleTimer = () => {
    timerRunning.value = !timerRunning.value;
  };

  const start = () => {
    timerRunning.value = true;
  };
  const stop = () => {
    timerRunning.value = false;
  };
  const reset = () => {
    elapsedTime.value = 0;
    startTime = 0; // 🔧 修复：重置开始时间
  };

  watch(timerRunning, (newVal) => {
    if (newVal) {
      // 🔧 修复：每次启动时重新设置开始时间
      startTime = Date.now();

      // 开始计时 - 10毫秒高精度定时器
      intervalId = setInterval(() => {
        elapsedTime.value = Date.now() - startTime;
      }, 10);

      console.log('🕐 定时器已启动，10ms间隔');
    } else {
      // 🔧 修复：正确清除定时器
      if (intervalId !== undefined) {
        clearInterval(intervalId as number);
        intervalId = undefined;
        console.log('⏹️ 定时器已停止');
      }
    }
  });

  const formatTime = computed(() => {
    if (elapsedTime.value === 0) return "";
    const minutes = Math.floor(elapsedTime.value / 60000);
    const seconds = Math.floor((elapsedTime.value % 60000) / 1000);
    const milliseconds = Math.floor((elapsedTime.value % 1000) / 10);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}.${milliseconds.toString().padStart(2, "0")}`;
  });

  return {
    elapsedTime,
    toggleTimer,
    formatTime,
    start,
    stop,
    reset,
  };
}
