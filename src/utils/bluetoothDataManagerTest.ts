/**
 * 蓝牙数据管理器测试工具
 * 用于验证设置更新后的蓝牙数据发送是否正常工作
 *
 * 项目中只有两种实际的蓝牙发送方案：
 * - traditional: iOS/Web 使用 @capacitor-community/bluetooth-le
 * - native: Android 使用 capacitor-kt-service
 */

import { bluetoothDataManager } from './bluetoothDataManager';
import { isPlatform } from '@ionic/vue';

export class BluetoothDataManagerTest {
  
  /**
   * 测试设置更新后的数据发送流程
   */
  public static async testSettingUpdateFlow(): Promise<boolean> {
    console.log("🧪 开始测试设置更新后的蓝牙数据发送流程");
    
    try {
      // 1. 获取当前平台信息
      const isAndroid = isPlatform('android');
      const isIOS = isPlatform('ios');
      const isWeb = !isAndroid && !isIOS;
      
      console.log("📱 当前平台:", {
        android: isAndroid,
        ios: isIOS,
        web: isWeb
      });
      
      // 2. 测试数据管理器的初始状态
      const initialStatus = bluetoothDataManager.getUpdateStatus();
      console.log("📊 初始状态:", initialStatus);
      
      // 3. 模拟设置更新
      console.log("🔧 模拟设置更新...");
      const updateResult = await bluetoothDataManager.updateSettingsAndSend();

      if (updateResult) {
        console.log("✅ 设置更新成功");

        // 4. 检查更新后的状态
        const updatedStatus = bluetoothDataManager.getUpdateStatus();
        console.log("📊 更新后状态:", updatedStatus);

        // 5. 验证活跃方案（简化版本不需要方案选择）
        console.log("🎯 使用响应式架构，无需方案选择");

        // 验证更新是否成功（响应式架构自动处理方案选择）
        console.log(`✅ 响应式架构更新成功`);

        // 6. 平台特定验证
        if (isAndroid) {
          return await this.testAndroidSpecific();
        } else {
          return await this.testWebIOSSpecific();
        }
        
      } else {
        console.error("❌ 设置更新失败");
        return false;
      }
      
    } catch (error) {
      console.error("❌ 测试过程中发生错误:", error);
      return false;
    }
  }
  
  /**
   * Android平台特定测试
   */
  private static async testAndroidSpecific(): Promise<boolean> {
    console.log("🤖 执行Android平台特定测试");
    
    try {
      // 检查原生蓝牙服务是否可用
      const { CapacitorKtService } = await import('capacitor-kt-service');
      
      // 测试获取当前蓝牙发送数据
      try {
        const currentData = await CapacitorKtService.getCurrentBluetoothSendData();
        console.log("📊 Android - 当前蓝牙发送数据:", currentData);
        
        if (currentData.success && currentData.data) {
          const hexData = currentData.data.map((byte: number) => 
            `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`
          ).join(' ');
          console.log("📊 Android - 数据十六进制:", hexData);
          return true;
        } else {
          console.warn("⚠️ Android - 无法获取当前蓝牙数据:", currentData.error);
          return false;
        }
      } catch (error) {
        console.error("❌ Android - 获取蓝牙数据失败:", error);
        return false;
      }
      
    } catch (error) {
      console.error("❌ Android - 原生服务不可用:", error);
      return false;
    }
  }
  
  /**
   * Web/iOS平台特定测试
   */
  private static async testWebIOSSpecific(): Promise<boolean> {
    console.log("🌐 执行Web/iOS平台特定测试");
    
    try {
      // 检查传统蓝牙方案
      const { useMessage } = await import("@/hooks/useMessage");
      const messageHook = useMessage();
      
      console.log("📊 Web/iOS - 传统方案状态:", {
        isServiceRunning: messageHook.isServiceRunning?.value,
        isTimerActive: messageHook.isTimerActive?.(),
        lastSendTime: messageHook.getLastSendTime?.(),
        failureCount: messageHook.getFailureCount?.()
      });
      
      return true;
      
    } catch (error) {
      console.error("❌ Web/iOS - 传统方案测试失败:", error);
      return false;
    }
  }
  
  /**
   * 测试数据一致性
   */
  public static async testDataConsistency(): Promise<boolean> {
    console.log("🧪 开始测试数据一致性");
    
    try {
      // 获取设置数据
      const { useSetting } = await import("@/hooks/useSetting");
      const { writeData } = useSetting();
      
      console.log("📊 当前writeData:", writeData.value);
      console.log("📊 writeData长度:", writeData.value?.length);
      
      if (!writeData.value || writeData.value.length !== 18) {
        console.error("❌ writeData长度不正确，期望18字节");
        return false;
      }
      
      // 验证数据格式
      const isValidFormat = this.validateDataFormat(writeData.value);
      if (!isValidFormat) {
        console.error("❌ 数据格式验证失败");
        return false;
      }
      
      console.log("✅ 数据一致性测试通过");
      return true;
      
    } catch (error) {
      console.error("❌ 数据一致性测试失败:", error);
      return false;
    }
  }
  
  /**
   * 验证数据格式
   */
  private static validateDataFormat(data: number[]): boolean {
    // 检查帧头
    if (data[0] !== 0x0F) {
      console.error("❌ 帧头不正确，期望0x0F，实际:", `0x${data[0].toString(16).toUpperCase()}`);
      return false;
    }
    
    // 检查结束位
    if (data[17] !== 0x0E) {
      console.error("❌ 结束位不正确，期望0x0E，实际:", `0x${data[17].toString(16).toUpperCase()}`);
      return false;
    }
    
    // 检查数据范围
    for (let i = 0; i < data.length; i++) {
      if (data[i] < 0 || data[i] > 255) {
        console.error(`❌ 数据第${i}位超出范围[0-255]:`, data[i]);
        return false;
      }
    }
    
    console.log("✅ 数据格式验证通过");
    return true;
  }
  
  /**
   * 运行完整测试套件
   */
  public static async runFullTestSuite(): Promise<void> {
    console.log("🧪 开始运行完整测试套件");
    
    const results = {
      settingUpdate: false,
      dataConsistency: false
    };
    
    // 测试1: 设置更新流程
    console.log("\n=== 测试1: 设置更新流程 ===");
    results.settingUpdate = await this.testSettingUpdateFlow();
    
    // 测试2: 数据一致性
    console.log("\n=== 测试2: 数据一致性 ===");
    results.dataConsistency = await this.testDataConsistency();
    
    // 输出测试结果
    console.log("\n=== 测试结果汇总 ===");
    console.table(results);
    
    const allPassed = Object.values(results).every(result => result === true);
    if (allPassed) {
      console.log("🎉 所有测试通过！蓝牙数据管理器工作正常");
    } else {
      console.error("❌ 部分测试失败，需要进一步检查");
    }
  }
}

// 导出便捷的测试函数
export const testBluetoothDataManager = BluetoothDataManagerTest.runFullTestSuite;
