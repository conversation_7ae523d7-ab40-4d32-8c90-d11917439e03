/**
 * 蓝牙 Composable
 * Vue 3 最佳实践：组件通过composable消费全局服务
 */

import { computed, readonly } from 'vue'
import { getBluetoothService } from '@/services/bluetoothService'
import { useBluetoothStore } from '@/store/useBluetoothStore'
import { useBleStore } from '@/store/useBleStore'
import { storeToRefs } from 'pinia'

/**
 * 蓝牙服务消费composable
 * 组件应该使用这个而不是直接调用hooks
 */
export function useBluetooth() {
  const bluetoothService = getBluetoothService()
  const bluetoothStore = useBluetoothStore()
  const bleStore = useBleStore()

  // 从store获取响应式状态
  const { 
    writeData, 
    currentSendData, 
    isBluetoothActive,
    currentMethod,
    lastUpdateTime
  } = storeToRefs(bluetoothStore)

  const { connectedDevice } = storeToRefs(bleStore)

  // 计算属性
  const isConnected = computed(() => connectedDevice.value?.isPaired || false)
  const canSend = computed(() => isConnected.value && isBluetoothActive.value)

  // 服务方法（这些会调用全局服务）
  const startSending = async (): Promise<boolean> => {
    return await bluetoothService.startSending()
  }

  const stopSending = async (): Promise<boolean> => {
    return await bluetoothService.stopSending()
  }

  const updateAndSend = async (): Promise<boolean> => {
    return await bluetoothService.updateAndSend()
  }

  // 数据更新方法（直接操作store）
  const updateData = (newData: number[]) => {
    bluetoothStore.updateWriteData(newData)
  }

  // 只读状态暴露
  return {
    // 状态（只读）
    writeData: readonly(writeData),
    currentSendData: readonly(currentSendData),
    isBluetoothActive: readonly(isBluetoothActive),
    currentMethod: readonly(currentMethod),
    lastUpdateTime: readonly(lastUpdateTime),
    connectedDevice: readonly(connectedDevice),
    
    // 计算属性
    isConnected,
    canSend,
    
    // 方法
    startSending,
    stopSending,
    updateAndSend,
    updateData
  }
}