<template>
  <ion-card v-if="showDebugPanel" class="debug-panel">
    <ion-card-header>
      <ion-card-title>
        <ion-icon :icon="bugOutline" />
        蓝牙服务调试面板
        <ion-button 
          fill="clear" 
          size="small" 
          @click="togglePanel"
          class="close-btn"
        >
          <ion-icon :icon="closeOutline" />
        </ion-button>
      </ion-card-title>
    </ion-card-header>
    
    <ion-card-content>
      <!-- 服务状态 -->
      <div class="status-section">
        <h4>服务状态</h4>
        <div class="status-grid">
          <div class="status-item">
            <ion-icon 
              :icon="serviceStatus?.isServiceRunning ? checkmarkCircleOutline : closeCircleOutline"
              :color="serviceStatus?.isServiceRunning ? 'top' : 'top'"
            />
            <span>服务运行: {{ serviceStatus?.isServiceRunning ? '是' : '否' }}</span>
          </div>
          
          <div class="status-item">
            <ion-icon 
              :icon="serviceStatus?.bluetoothManagerInitialized ? checkmarkCircleOutline : closeCircleOutline"
              :color="serviceStatus?.bluetoothManagerInitialized ? 'top' : 'top'"
            />
            <span>蓝牙管理器: {{ serviceStatus?.bluetoothManagerInitialized ? '已初始化' : '未初始化' }}</span>
          </div>
          
          <div class="status-item">
            <ion-icon 
              :icon="isHealthy ? checkmarkCircleOutline : warningOutline"
              :color="isHealthy ? 'top' : 'top'"
            />
            <span>服务健康: {{ isHealthy ? '正常' : '异常' }}</span>
          </div>
        </div>
        
        <div class="details" v-if="serviceStatus">
          <p><strong>详细信息:</strong> {{ serviceStatus.bluetoothManagerDetails }}</p>
          <p><strong>更新时间:</strong> {{ formatTime(serviceStatus.timestamp) }}</p>
          <p v-if="serviceStatus.error" class="error-text">
            <strong>错误:</strong> {{ serviceStatus.error }}
          </p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions-section">
        <h4>调试操作</h4>
        <div class="button-grid">
          <ion-button 
            size="small" 
            fill="outline" 
            @click="refreshStatus"
            :disabled="isLoading"
          >
            <ion-icon :icon="refreshOutline" slot="start" />
            刷新状态
          </ion-button>
          
          <ion-button 
            size="small" 
            fill="outline" 
            @click="checkServiceHealth"
            :disabled="isLoading"
          >
            <ion-icon :icon="heartOutline" slot="start" />
            健康检查
          </ion-button>
          
          <ion-button 
            size="small" 
            fill="outline" 
            @click="resetService"
            :disabled="isLoading"
          >
            <ion-icon :icon="refreshCircleOutline" slot="start" />
            重置服务
          </ion-button>
          
          <ion-button 
            size="small" 
            fill="outline" 
            @click="exportLogs"
            :disabled="isLoading"
          >
            <ion-icon :icon="downloadOutline" slot="start" />
            导出日志
          </ion-button>
        </div>
      </div>
      
      <!-- 实时数据 -->
      <div class="data-section" v-if="currentData">
        <h4>当前数据</h4>
        <div class="data-display">
          <code>{{ formatDataArray(currentData) }}</code>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section" v-if="stats">
        <h4>发送统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="label">总发送:</span>
            <span class="value">{{ stats.totalSent }}</span>
          </div>
          <div class="stat-item">
            <span class="label">成功:</span>
            <span class="value">{{ stats.successCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">错误:</span>
            <span class="value">{{ stats.errorCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">错误率:</span>
            <span class="value">{{ errorRate }}%</span>
          </div>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
  
  <!-- 浮动调试按钮 -->
  <ion-fab 
    v-if="!showDebugPanel && isDevelopment" 
    vertical="bottom" 
    horizontal="end" 
    slot="fixed"
  >
    <ion-fab-button 
      size="small" 
      @click="togglePanel"
      color="secondary"
    >
      <ion-icon :icon="bugOutline" />
    </ion-fab-button>
  </ion-fab>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon,
  IonFab,
  IonFabButton
} from '@ionic/vue';
import {
  bugOutline,
  closeOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  warningOutline,
  refreshOutline,
  heartOutline,
  refreshCircleOutline,
  downloadOutline
} from 'ionicons/icons';
import { bluetoothServiceHelper, type BluetoothServiceStatus } from '@/utils/bluetoothServiceHelper';
import { useToast } from '@/hooks/useToast';

// Props
interface Props {
  visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
});

// 响应式数据
const showDebugPanel = ref(props.visible);
const serviceStatus = ref<BluetoothServiceStatus | null>(null);
const currentData = ref<number[] | null>(null);
const stats = ref<any>(null);
const isLoading = ref(false);
const isHealthy = ref(false);

// 计算属性
const isDevelopment = computed(() => process.env.NODE_ENV === 'development');
const errorRate = computed(() => {
  if (!stats.value || stats.value.totalSent === 0) return 0;
  return ((stats.value.errorCount / stats.value.totalSent) * 100).toFixed(1);
});

// Hooks
const { presentToast } = useToast();

// 定时器
let statusInterval: NodeJS.Timeout | null = null;

// 方法
const togglePanel = () => {
  showDebugPanel.value = !showDebugPanel.value;
  if (showDebugPanel.value) {
    startStatusMonitoring();
  } else {
    stopStatusMonitoring();
  }
};

const refreshStatus = async () => {
  isLoading.value = true;
  try {
    await updateAllStatus();
    await presentToast('状态已刷新', 'top');
  } catch (error) {
    console.error('刷新状态失败:', error);
    await presentToast('刷新状态失败', 'top');
  } finally {
    isLoading.value = false;
  }
};

const checkServiceHealth = async () => {
  isLoading.value = true;
  try {
    const healthy = await bluetoothServiceHelper.isServiceHealthy();
    isHealthy.value = healthy;
    await presentToast(
      healthy ? '服务健康状态正常' : '服务健康状态异常', 
      healthy ? 'top' : 'top'
    );
  } catch (error) {
    console.error('健康检查失败:', error);
    await presentToast('健康检查失败', 'top');
  } finally {
    isLoading.value = false;
  }
};

const resetService = async () => {
  isLoading.value = true;
  try {
    bluetoothServiceHelper.resetInitializationStatus();
    await updateAllStatus();
    await presentToast('服务状态已重置', 'top');
  } catch (error) {
    console.error('重置服务失败:', error);
    await presentToast('重置服务失败', 'top');
  } finally {
    isLoading.value = false;
  }
};

const exportLogs = async () => {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      serviceStatus: serviceStatus.value,
      currentData: currentData.value,
      stats: stats.value,
      isHealthy: isHealthy.value
    };
    
    const dataStr = JSON.stringify(logData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `bluetooth-service-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    await presentToast('调试日志已导出', 'top');
  } catch (error) {
    console.error('导出日志失败:', error);
    await presentToast('导出日志失败', 'top');
  }
};

const updateAllStatus = async () => {
  try {
    // 获取服务状态
    serviceStatus.value = await bluetoothServiceHelper.getServiceInitializationStatus();
    
    // 获取当前数据
    const dataResult = await bluetoothServiceHelper.getCurrentBluetoothSendData();
    currentData.value = dataResult.success ? dataResult.data || null : null;
    
    // 获取统计信息
    const statsResult = await bluetoothServiceHelper.getBluetoothSendingStats();
    stats.value = statsResult.success ? statsResult : null;
    
    // 检查健康状态
    isHealthy.value = await bluetoothServiceHelper.isServiceHealthy();
  } catch (error) {
    console.error('更新状态失败:', error);
  }
};

const startStatusMonitoring = () => {
  if (statusInterval) {
    clearInterval(statusInterval);
  }
  
  // 立即更新一次
  updateAllStatus();
  
  // 每5秒更新一次
  statusInterval = setInterval(updateAllStatus, 5000);
};

const stopStatusMonitoring = () => {
  if (statusInterval) {
    clearInterval(statusInterval);
    statusInterval = null;
  }
};

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString();
};

const formatDataArray = (data: number[]): string => {
  return data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
};

// 生命周期
onMounted(() => {
  if (showDebugPanel.value) {
    startStatusMonitoring();
  }
});

onUnmounted(() => {
  stopStatusMonitoring();
});
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 20px;
  width: calc(100% - 36px);
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
  background: var(--ion-color-light);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.close-btn {
  float: right;
  margin-top: -8px;
}

.status-section,
.actions-section,
.data-section,
.stats-section {
  margin-bottom: 16px;
}

.status-section h4,
.actions-section h4,
.data-section h4,
.stats-section h4 {
  margin: 0 0 8px 0;
  color: var(--ion-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.details {
  margin-top: 12px;
  font-size: 11px;
  color: var(--ion-color-medium);
}

.details p {
  margin: 4px 0;
}

.error-text {
  color: var(--ion-color-danger) !important;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.data-display {
  background: var(--ion-color-light-shade);
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 10px;
  word-break: break-all;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  padding: 4px 8px;
  background: var(--ion-color-light-shade);
  border-radius: 4px;
}

.label {
  color: var(--ion-color-medium);
}

.value {
  font-weight: 600;
}

ion-fab {
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
</style>