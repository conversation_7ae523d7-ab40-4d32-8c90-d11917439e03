# KT Smart

一个基于 Ionic Vue 的智能蓝牙设备管理应用，支持跨平台的蓝牙通信和设备控制。

## 📱 项目概述

KT Smart 是一个现代化的移动应用，专为智能设备的蓝牙通信和控制而设计。应用采用三层蓝牙架构，支持 iOS、Android 和 Web 平台，提供统一的用户体验和高效的设备管理功能。

### 🎯 核心功能

- 🔗 **跨平台蓝牙通信** - 支持 iOS、Android、Web
- ⚙️ **设备参数配置** - 实时设置和同步设备参数
- 📊 **数据可视化** - 图表展示设备状态和数据
- 🗺️ **地图导航** - 集成 Mapbox 地图和导航功能
- 🔧 **调试工具** - 完整的蓝牙调试和测试工具

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI库**: Ionic Vue 8.6.2
- **状态管理**: Pinia
- **构建工具**: Vite 6.1.0
- **移动端**: Capacitor 7.4.0

### 蓝牙架构（响应式设计）

```
┌─────────────────────────────────────────────────────────────────┐
│                          应用层                                 │
│  SettingPage, HomePage, Tab1Page, DataDiagnosticPage 等        │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                    响应式管理层                                 │
│                 useBluetoothManager                             │
│              (统一的响应式数据管理)                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  useBluetoothData │  │ useBluetoothSender│  │useBluetoothStore│  │
│  │   (数据计算)     │  │   (发送管理)     │  │   (状态管理)   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────────┐
│                    平台适配层                                   │
│                useSmartBluetoothMessage                         │
│              (智能平台选择和路由)                               │
└─────────────┬───────────────────────┬───────────────────────────┘
              │                       │
┌─────────────▼─────────┐   ┌─────────▼─────────────────────────┐
│      传统方案         │   │         原生方案                 │
│    useMessage         │   │  useNativeBluetoothMessage       │
│    (iOS/Web)          │   │      (Android)                   │
│                       │   │                                   │
│ ┌─────────────────┐   │   │ ┌─────────────────────────────┐   │
│ │@capacitor-community│   │   │   capacitor-kt-service      │   │
│ │  bluetooth-le   │   │   │ │     (原生蓝牙服务)         │   │
│ └─────────────────┘   │   │ └─────────────────────────────┘   │
└───────────────────────┘   └───────────────────────────────────┘
              │                       │
              └───────────┬───────────┘
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      蓝牙设备层                                 │
│                   (106ms 频率通信)                              │
└─────────────────────────────────────────────────────────────────┘
```

#### 新架构特性

1. **响应式驱动**
   - **自动更新**: 设置变化自动触发数据重新计算
   - **单一数据源**: 全局唯一的 writeData，避免多实例问题
   - **实时同步**: 定时器每次发送前获取最新响应式数据
   - **数据一致性**: 确保发送数据与当前设置完全同步

2. **智能定时器管理**
   - **单一定时器**: 应用启动后只运行一个定时器实例
   - **重复保护**: 防止多次启动导致的定时器混乱
   - **平台优化**: iOS/Web/Android 各自使用最佳定时器策略
   - **数据同步**: 只更新数据内容，不重启定时器

3. **模块化设计**
   - **useBluetoothData**: 负责蓝牙数据的响应式计算
   - **useBluetoothSender**: 负责数据发送和频率控制
   - **useBluetoothStore**: 负责全局状态管理
   - **useBluetoothManager**: 统一的管理接口

4. **平台智能适配**
   - **自动选择**: 根据平台自动选择最佳方案
   - **无缝切换**: 应用层无需关心平台差异
   - **统一API**: 一致的接口体验

### 响应式数据流程图

#### 1. 响应式数据更新流程

```text
┌─────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│   用户操作  │───▶│     Store 状态       │───▶│   响应式计算        │
│ (设置修改)  │    │   (Pinia Store)      │    │                     │
└─────────────┘    └──────────────────────┘    │ ┌─────────────────┐ │
                              │                │ │ useBluetoothData│ │
                              │                │ │  (自动重新计算) │ │
                              ▼                │ └─────────────────┘ │
                   ┌──────────────────────┐    │ ┌─────────────────┐ │
                   │   writeData 更新     │◄───┤ │   校验和计算    │ │
                   │    (全局唯一)        │    │ │   参数映射      │ │
                   └──────────────────────┘    │ │   数据组装      │ │
                              │                │ └─────────────────┘ │
                              ▼                └─────────────────────┘
                   ┌──────────────────────┐
                   │   定时器发送前       │
                   │ 获取最新响应式数据   │ ◄── 🔧 关键修复点
                   └──────────────────────┘
                              │
                              ▼
                   ┌──────────────────────┐
                   │   发送到设备         │
                   │   (106ms频率)        │
                   └──────────────────────┘
```

#### 2. 蓝牙管理器统一流程

```text
┌─────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│ 应用组件    │───▶│  useBluetoothManager │───▶│   统一数据管理      │
│ 调用接口    │    │   (统一管理器)       │    │                     │
└─────────────┘    └──────────────────────┘    │ ┌─────────────────┐ │
                              │                │ │ useBluetoothData│ │
                              │                │ │ useBluetoothSender│ │
                              ▼                │ │useBluetoothStore│ │
                   ┌──────────────────────┐    │ └─────────────────┘ │
                   │   平台智能选择       │    └─────────────────────┘
                   └──────────────────────┘               │
                              │                           ▼
                              ▼                ┌─────────────────────┐
                   ┌──────────────────────┐    │   跨平台发送        │
                   │  useSmartBluetooth   │───▶│                     │
                   │     Message          │    │ ┌─────────────────┐ │
                   │   (平台路由)         │    │ │ iOS/Web:        │ │
                   └──────────────────────┘    │ │ useMessage      │ │
                                               │ └─────────────────┘ │
                                               │ ┌─────────────────┐ │
                                               │ │ Android:        │ │
                                               │ │useNativeBluetooth│ │
                                               │ │   Message       │ │
                                               │ └─────────────────┘ │
                                               └─────────────────────┘
```

#### 3. 平台智能选择流程

```text
┌─────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│  应用启动   │───▶│   检测运行平台       │───▶│     方案选择        │
└─────────────┘    └──────────────────────┘    └─────────────────────┘
                              │                           │
                              ▼                           ▼
                   ┌──────────────────────┐    ┌─────────────────────┐
                   │   检查服务状态       │    │   Android 平台      │
                   │                      │    │  ┌───────────────┐  │
                   │ ┌──────────────────┐ │    │  │ 优先选择:     │  │
                   │ │ native 运行中?   │ │    │  │ native 方案   │  │
                   │ └──────────────────┘ │    │  └───────────────┘  │
                   │ ┌──────────────────┐ │    │  ┌───────────────┐  │
                   │ │traditional运行中?│ │    │  │ 备选:         │  │
                   │ └──────────────────┘ │    │  │traditional方案│  │
                   └──────────────────────┘    │  └───────────────┘  │
                              │                └─────────────────────┘
                              ▼                ┌─────────────────────┐
                   ┌──────────────────────┐    │   iOS/Web 平台      │
                   │   根据平台选择       │    │  ┌───────────────┐  │
                   │   默认方案           │───▶│  │ 优先选择:     │  │
                   └──────────────────────┘    │  │traditional方案│  │
                                               │  └───────────────┘  │
                                               │  ┌───────────────┐  │
                                               │  │ 备选:         │  │
                                               │  │ native 方案   │  │
                                               │  └───────────────┘  │
                                               └─────────────────────┘
```

#### 4. 蓝牙数据协议流程

```text
┌─────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│  设置参数   │───▶│    数据组装          │───▶│    校验计算         │
│ (1-11字节)  │    │                      │    │                     │
└─────────────┘    │ ┌──────────────────┐ │    │ ┌─────────────────┐ │
┌─────────────┐    │ │ 0x0F (帧头)      │ │    │ │ 字节5: 1-4,6-11 │ │
│  导航数据   │───▶│ │ 设置数据(1-11)   │ │───▶│ │ 异或校验        │ │
│(12-16字节)  │    │ │ 导航数据(12-16)  │ │    │ │ 字节16: 1-15    │ │
└─────────────┘    │ │ 0x0E (帧尾)      │ │    │ │ 异或校验(跳过5) │ │
                   │ └──────────────────┘ │    │ └─────────────────┘ │
                   └──────────────────────┘    └─────────────────────┘
                              │                           │
                              ▼                           ▼
                   ┌──────────────────────┐    ┌─────────────────────┐
                   │   完整数据包         │───▶│   106ms频率发送     │
                   │   (18字节)           │    │                     │
                   └──────────────────────┘    └─────────────────────┘
```

### 核心依赖

```json
{
  "@ionic/vue": "8.6.2",
  "@capacitor/core": "7.0.1",
  "@capacitor-community/bluetooth-le": "7.1.1",
  "capacitor-kt-service": "file:capacitor-kt-service",
  "mapbox-gl": "3.13.0",
  "@antv/g2": "5.2.12",
  "vue": "3.5.17",
  "pinia": "3.0.1"
}
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn
- iOS 开发需要 Xcode
- Android 开发需要 Android Studio

### 安装依赖

```bash
npm install
```

### 开发命令

```bash
# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码检查和修复
npm run lint
```

### 测试命令

```bash
# 运行所有单元测试
npm run test:unit

# 运行一致性测试（验证新旧架构一致性）
npm run test:unit -- consistency.spec.ts

# 运行新架构功能测试
npm run test:unit -- useBluetoothData.spec.ts

# 运行测试并查看详细输出
npm run test:unit -- --reporter=verbose

# 验证架构替换完整性
chmod +x verify-final.sh && ./verify-final.sh
```

### 构建命令

```bash
# 生产版本构建（完整优化）
npm run build

# 开发版本快速构建（用于调试）
npm run build:fast

# 构建分析
npm run build:analyze
```

### 移动端调试

```bash
# iOS 调试
npm run debug:ios

# Android 调试  
npm run debug:android

# iOS 实时重载
npm run live:ios

# Android 实时重载
npm run live:android
```

## 🔧 开发指南

### 响应式蓝牙数据管理

#### 新架构使用方式

```typescript
import { useBluetoothManager } from "@/composables/useBluetoothManager";

// 获取统一的蓝牙管理器
const bluetoothManager = useBluetoothManager();
const { writeData, updateAndSend, startSending, stopSending, isSending } = bluetoothManager;

// 响应式数据 - 自动更新
console.log('当前蓝牙数据:', writeData.value);

// 手动触发更新和发送
await updateAndSend();

// 启动/停止蓝牙发送
await startSending();
await stopSending();
```

#### 响应式数据计算

```typescript
import { useBluetoothData } from "@/composables/useBluetoothData";

// 获取响应式计算的蓝牙数据
const { writeData, updateNavigationData } = useBluetoothData();

// 数据会自动响应 Store 变化
// 无需手动调用更新函数

// 更新导航数据
updateNavigationData(0x81, 100, 50, 200);
```

#### 兼容性接口（保留）

```typescript
import { useSmartBluetoothMessage } from "@/hooks/useSmartBluetoothMessage";

// 仍然支持原有的智能选择接口
const {
  startSending,
  stopSending,
  isServiceRunning
} = useSmartBluetoothMessage();
```

### 响应式架构优势

#### 🎯 核心优势

1. **数据一致性保障**
   - 全局唯一的 writeData 数据源
   - 消除多实例导致的数据不同步问题
   - 定时器实时获取最新响应式数据
   - 确保发送数据与当前设置完全同步

2. **定时器管理优化**
   - 单一定时器实例，避免多定时器混乱
   - 智能重复启动保护机制
   - 平台特定的最佳定时器策略
   - 数据更新不影响定时器运行

3. **开发体验提升**
   - 自动响应式更新，无需手动调用
   - 完整的 TypeScript 类型支持
   - 清晰的数据流和状态追踪
   - 简化的定时器管理逻辑

4. **性能优化**
   - 按需计算，避免不必要的重复计算
   - 减少内存使用和响应式对象创建
   - 智能的平台选择和路由
   - 避免定时器重启带来的性能开销

5. **维护性改善**
   - 职责清晰：Store 管理状态，Composable 管理业务逻辑
   - 易于扩展：新功能可以轻松集成到响应式架构
   - 测试友好：更容易进行单元测试和集成测试
   - 简化的定时器生命周期管理

#### 🔄 迁移对比

| 特性 | 旧架构 | 新架构 |
|------|--------|--------|
| 数据源 | 多个独立实例 | 全局唯一响应式 |
| 更新方式 | 手动调用 updateSetting | 自动响应式更新 |
| 数据一致性 | 可能不同步 | 始终一致 |
| 定时器管理 | 可能多实例运行 | 单一实例 + 重复保护 |
| 数据同步 | 缓存数据可能过期 | 实时获取最新数据 |
| 类型安全 | 部分支持 | 完整支持 |
| 调试体验 | 复杂 | 清晰直观 |
| 性能 | 多实例开销 | 优化的单实例 |

### 环境变量配置

项目支持不同环境的配置：

#### 开发环境 (.env.development)

```bash
NODE_ENV=development
VITE_APP_TITLE=KT Smart (Dev)
VITE_DEBUG=true
VITE_BLUETOOTH_DEBUG=true
```

#### 生产环境 (.env.production)

```bash
NODE_ENV=production
VITE_APP_TITLE=KT Smart
VITE_DEBUG=false
VITE_BLUETOOTH_DEBUG=false
```

### 蓝牙数据协议

- **发送频率**: 106毫秒
- **数据格式**: 18字节，以0x0F开头，0x0E结尾
- **数据结构**:
  - 字节1-11: 设置参数
  - 字节12-16: 导航数据
  - 字节5,16: 校验位

### 定时器管理策略

#### 🎯 智能定时器选择
- **应用启动时**: 根据平台智能选择最佳定时器策略
- **iOS 平台**: 使用 `createUltimateTimer` (iOS Worker 定时器优先)
- **Web 平台**: 使用 `createReliableTimer` (Web Worker/RAF 定时器)
- **Android 平台**: 使用原生定时器服务 (Java 层实现)

#### 🔄 定时器生命周期
- **启动原则**: 应用启动后选择一个定时器，持续运行
- **重复保护**: 定时器运行中时，拒绝重复启动请求
- **数据更新**: 只更新发送数据内容，不重启定时器
- **响应式同步**: 每次发送前获取最新的响应式数据

#### 📊 数据同步机制
```typescript
// 定时器发送函数中的数据同步
sendDataFunction = async () => {
  // 🔧 每次发送前都获取最新的响应式数据
  currentSendData = [...writeData.value];

  // 发送最新数据到设备
  await sendToDevice(currentSendData);
};
```

## 📱 平台特性

### iOS
- 使用 `@capacitor-community/bluetooth-le`
- 支持后台蓝牙通信
- 完整的权限管理

### Android  
- 使用自定义 `capacitor-kt-service`
- 原生蓝牙优化
- 高性能数据传输

### Web
- 使用 Web Bluetooth API
- 渐进式 Web 应用支持
- 跨浏览器兼容

## 🧪 测试

### 单元测试
```bash
npm run test:unit
```

### E2E 测试
```bash
npm run test:e2e
```

### 蓝牙功能测试
在开发环境中，设置页面提供了蓝牙数据管理器测试工具：
```typescript
import { testBluetoothDataManager } from "@/utils/bluetoothDataManagerTest";

// 运行完整测试套件
await testBluetoothDataManager();
```

## 📦 构建优化

### 开发版本 (build:fast)
- 无代码分割 - 加快构建速度
- 无压缩 - 大幅提升构建速度
- 保留调试信息 - 便于开发调试
- 启用源码映射 - 便于错误定位

### 生产版本 (build)
- 代码分割 - 优化加载性能
- Terser 压缩 - 减小包体积
- 移除 console - 清理生产代码
- TypeScript 检查 - 确保代码质量

## 🔍 调试工具

### 蓝牙调试
- 实时数据监控
- 连接状态检查
- 数据发送验证
- 平台兼容性测试

### 开发工具
- Vue DevTools 支持
- 热重载开发
- 源码映射调试
- 性能分析工具

## 🎯 架构升级总结

### ✅ 升级成果

本项目已成功完成从传统实例化模式到响应式架构的全面升级：

1. **数据一致性**: 解决了多实例导致的数据不同步问题
2. **性能优化**: 减少了不必要的计算和内存使用
3. **开发体验**: 提供了更好的类型安全和调试体验
4. **维护性**: 显著提升了代码的可维护性和可扩展性

### 📊 测试覆盖

- **131/131 测试通过** (100% 通过率)
- **完整的一致性验证** (新旧架构输出完全一致)
- **全面的功能测试** (覆盖所有关键场景)
- **类型安全保障** (无 TypeScript 错误)

### 🔄 迁移完成

所有关键文件已完成架构迁移：
- ✅ useMessage.ts (Web端核心)
- ✅ useNativeBluetoothMessage.ts (Native端核心)
- ✅ useSmartBluetoothMessage.ts (智能路由)
- ✅ useBluetoothAutoSender.ts (自动发送)
- ✅ HomePage.vue, Tab1Page.vue 等页面组件
- ✅ 测试套件清理和优化

## 📄 许可证

本项目为私有项目，版权所有。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📂 项目结构

```text
kt-smart/
├── src/
│   ├── components/          # 可复用组件
│   ├── hooks/              # 组合式API
│   │   ├── useMessage.ts           # 传统蓝牙方案
│   │   ├── useNativeBluetoothMessage.ts  # 原生蓝牙方案
│   │   └── useSmartBluetoothMessage.ts   # 智能选择器
│   ├── store/              # Pinia 状态管理
│   ├── utils/              # 工具函数
│   │   ├── bluetoothDataManager.ts       # 蓝牙数据管理器
│   │   └── bluetoothDataManagerTest.ts   # 测试工具
│   ├── views/              # 页面组件
│   │   └── SettingPage.vue             # 设置页面
│   └── router/             # 路由配置
├── capacitor-kt-service/   # Android 原生蓝牙服务
├── public/                 # 静态资源
└── dist/                   # 构建输出
```

### 组件关系图

```text
┌─────────────────────────────────────────────────────────────────┐
│                        应用组件层                               │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ App.vue     │  │ HomePage    │  │ SettingPage │  │ 其他页面│ │
│  │ (应用入口)  │  │ (主页)      │  │ (设置页面)  │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│         │                │                │                     │
└─────────┼────────────────┼────────────────┼─────────────────────┘
          │                │                │
          ▼                ▼                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Hook 组合层                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              useSmartBluetoothMessage                       │ │
│  │                   (统一API接口)                            │ │
│  └─────────────────────┬───────────────────────────────────────┘ │
│                        │                                         │
│  ┌─────────────────────▼───────────────────────────────────────┐ │
│  │              bluetoothDataManager                           │ │
│  │                 (数据管理器)                               │ │
│  └─────────────────────┬───────────────────────────────────────┘ │
│                        │                                         │
│  ┌─────────────────────▼───────────────────────────────────────┐ │
│  │  useMessage          │         useNativeBluetoothMessage    │ │
│  │  (传统方案)          │              (原生方案)             │ │
│  └──────────────────────┴─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
          │                                        │
          ▼                                        ▼
┌─────────────────────────────────────────────────────────────────┐
│                      底层服务层                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────┐  ┌─────────────────────────────┐ │
│  │  @capacitor-community/      │  │    capacitor-kt-service     │ │
│  │     bluetooth-le            │  │      (Android 原生)         │ │
│  │    (iOS/Web 通用)           │  │                             │ │
│  └─────────────────────────────┘  └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
          │                                        │
          ▼                                        ▼
┌─────────────────────────────────────────────────────────────────┐
│                      设备通信层                                │
├─────────────────────────────────────────────────────────────────┤
│                    ┌─────────────────┐                          │
│                    │   蓝牙设备      │                          │
│                    │  (106ms频率)    │                          │
│                    │   数据通信      │                          │
│                    └─────────────────┘                          │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流向图

```text
用户操作 ──┐
          │
设置更新 ──┼──▶ SettingPage.vue
          │         │
导航数据 ──┘         ▼
                bluetoothDataManager.updateSettingsAndSend()
                     │
                     ├─ 更新设置数据 (Android: 原生方案 / iOS/Web: 通用方案)
                     │
                     ├─ 检测活跃蓝牙方案
                     │
                     └─ 发送数据到活跃方案
                            │
                ┌───────────┴───────────┐
                ▼                       ▼
        useMessage                useNativeBluetoothMessage
        (iOS/Web)                      (Android)
                │                       │
                ▼                       ▼
    @capacitor-community/        capacitor-kt-service
       bluetooth-le                 (原生服务)
                │                       │
                └───────────┬───────────┘
                            ▼
                      蓝牙设备通信
                     (106ms 频率发送)
```

## 🔧 常见问题

### Q: 蓝牙连接失败怎么办？

A:

1. 检查设备蓝牙权限
2. 确认设备支持蓝牙功能
3. 查看控制台错误日志
4. 使用调试工具测试连接

### Q: Android 和 iOS 数据不同步？

A:

1. 检查平台特定的设置更新逻辑
2. 验证数据管理器的方案选择
3. 确认校验位计算正确

### Q: 构建速度慢怎么优化？

A:

1. 使用 `npm run build:fast` 进行开发构建
2. 检查依赖项是否过大
3. 考虑使用代码分割优化

## 📈 性能优化

### 蓝牙通信优化
- 106ms 高频发送优化
- 数据缓存机制
- 智能方案选择
- 避免重复调用

### 构建优化
- 代码分割策略
- 依赖项优化
- 压缩和混淆
- 资源懒加载

### 运行时优化

- 组件懒加载
- 状态管理优化
- 内存泄漏防护
- 性能监控

## 📞 支持

如有问题或建议，请联系开发团队。

---

**KT Smart** - 智能设备蓝牙管理的最佳选择 🚀
