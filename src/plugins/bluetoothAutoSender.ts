import { App } from 'vue';
import { useBluetoothAutoSender } from '@/hooks/useBluetoothAutoSender';

/**
 * 蓝牙自动发送插件
 * 在应用启动时自动初始化全局蓝牙连接监听器
 */
export default {
  install(app: App) {
    // 在应用启动时初始化蓝牙自动发送监听器
    const bluetoothAutoSender = useBluetoothAutoSender();
    
    // 启动全局监听器
    bluetoothAutoSender.startBluetoothConnectionListener();
    
    console.log('🎯 蓝牙自动发送插件已安装并启动全局监听器');
    
    // 将蓝牙自动发送实例添加到全局属性（可选，用于调试）
    app.config.globalProperties.$bluetoothAutoSender = bluetoothAutoSender;
    
    // 提供全局方法（可选）
    app.provide('bluetoothAutoSender', bluetoothAutoSender);
  }
};
