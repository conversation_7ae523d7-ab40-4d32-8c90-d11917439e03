import Foundation
import Capacitor

@objc public class CapacitorMapboxNavigation: NSObject {

    // 显示导航
    @objc func show(_ call: CAPPluginCall) {
        // 导航实现将在这里添加
        call.resolve()
    }

    // 获取导航历史
    @objc func history(_ call: CAPPluginCall) {
        // 历史记录实现将在这里添加
        call.resolve()
    }

    // 请求权限
    @objc func requestPermissions(_ call: CAPPluginCall) {
        // 权限请求实现将在这里添加
        call.resolve()
    }

    // 检查权限
    @objc func checkPermissions(_ call: CAPPluginCall) {
        // 权限检查实现将在这里添加
        call.resolve()
    }
}
