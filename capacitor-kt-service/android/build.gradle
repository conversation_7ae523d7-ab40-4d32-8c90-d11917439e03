ext {
    junitVersion = project.hasProperty('junitVersion') ? rootProject.ext.junitVersion : '4.13.2'
    androidxAppCompatVersion = project.hasProperty('androidxAppCompatVersion') ? rootProject.ext.androidxAppCompatVersion : '1.7.0'
    androidxJunitVersion = project.hasProperty('androidxJunitVersion') ? rootProject.ext.androidxJunitVersion : '1.2.1'
    androidxEspressoCoreVersion = project.hasProperty('androidxEspressoCoreVersion') ? rootProject.ext.androidxEspressoCoreVersion : '3.6.1'
    androidxCoreVersion = project.hasProperty('androidxCoreVersion') ? rootProject.ext.androidxCoreVersion : '1.12.0'
}

buildscript {
    ext.kotlin_version = project.hasProperty("kotlin_version") ? rootProject.ext.kotlin_version : '1.9.25'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    namespace "com.kunteng.plugins.kt"
    compileSdk project.hasProperty('compileSdkVersion') ? rootProject.ext.compileSdkVersion : 35
    defaultConfig {
        minSdkVersion project.hasProperty('minSdkVersion') ? rootProject.ext.minSdkVersion : 23
        targetSdkVersion project.hasProperty('targetSdkVersion') ? rootProject.ext.targetSdkVersion : 35
        versionCode 1
        versionName "1.0.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        dataBinding true
    }
    dataBinding {
        enabled = true
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }
}

kotlin {
    jvmToolchain(21)
}

repositories {
    google()
    mavenCentral()
    // 添加 Mapbox 官方仓库
    maven {
        url 'https://api.mapbox.com/downloads/v2/releases/maven'
        authentication {
            basic(BasicAuthentication)
        }
        credentials {
            // 使用你的 Mapbox Downloads Token（用于下载SDK）
            username = 'mapbox'
            password = project.findProperty('MAPBOX_DOWNLOADS_TOKEN') ?: ''
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':capacitor-android')
    
    // Android支持库
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.core:core:$androidxCoreVersion"
    implementation "androidx.databinding:databinding-runtime:8.5.2"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    
    // Mapbox Navigation SDK
    implementation "com.mapbox.navigationcore:copilot:3.10.0"
    implementation "com.mapbox.navigationcore:ui-maps:3.10.0"
    implementation "com.mapbox.navigationcore:voice:3.10.0"
    implementation "com.mapbox.navigationcore:tripdata:3.10.0"
    implementation "com.mapbox.navigationcore:android:3.10.0"
    implementation "com.mapbox.navigationcore:ui-components:3.10.0"
    // Mapbox Maps SDK (for LocationPuck2D)
    implementation "com.mapbox.maps:android:11.4.0"
    
    // Mapbox Search SDK
    implementation "com.mapbox.search:mapbox-search-android:2.14.0"
    implementation "com.mapbox.search:mapbox-search-android-ui:2.14.0"
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.3.0'

    // 测试依赖
    testImplementation "junit:junit:$junitVersion"
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.mockito:mockito-inline:5.8.0'
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
}
