import { computed, watch, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useSettingStore } from '@/store/useSettingStore'
import { useDashboardStore } from '@/store/useDashboardStore'
import { WriteData } from '@/const/ble.const'
import { GearDirection, LightDirection } from '@/const/bike.const'

// 🔧 全局导航数据状态，确保数据持久化
const globalNavigationData = ref({
  byte12: 0x00,
  byte13: 0x00,
  byte14: 0x00,
  byte15: 0x00
})

/**
 * 蓝牙数据计算 Composable
 * 使用 Vue 3 最佳实践：响应式计算 + Store 状态管理
 * 
 * 这个 composable 负责：
 * 1. 基于设置数据计算 writeData
 * 2. 响应式更新，无需手动调用
 * 3. 纯函数式，无副作用
 */
export function useBluetoothData() {
  const settingStore = useSettingStore()
  const dashboardStore = useDashboardStore()

  // 🔧 用于避免重复日志的变量
  let lastDataStr = ''

  // 获取响应式状态
  const {
    dimension,
    maxSpeed,
    p1, p2, p3, p4, p5,
    c1, c2, c3, c4, c5, c7, c12, c13, c14,
    percent,
    handlebarMaxSpeed
  } = storeToRefs(settingStore)
  
  const { gearPosition, lightStatus } = storeToRefs(dashboardStore)
  
  // 工具函数 - 与 useSetting.ts 中的 Int2Bytes 保持一致
  const Int2Bytes = (value: any): number => {
    // 处理各种输入类型
    if (value === null || value === undefined || isNaN(value)) {
      console.log(`🔧 Int2Bytes: 无效值 ${value}，使用默认值0`)
      return 0
    }

    // 转换为数字
    const numValue = typeof value === 'string' ? parseFloat(value) : Number(value)

    // 如果转换后仍然是 NaN，返回 0
    if (isNaN(numValue)) {
      console.log(`🔧 Int2Bytes: 无效值 ${value}，使用默认值0`)
      return 0
    }

    // 使用位运算确保在 0-255 范围内（与测试用例一致）
    return numValue & 0xFF
  }
  
  // 🔧 导航数据现在使用全局状态管理

  // 响应式计算 writeData
  const writeData = computed(() => {
    const data = [...WriteData] // 从基础数据开始

    // 🔧 保留现有的导航数据（索引12-15），避免被重置
    // 这与 useSetting.ts 中的 updateSetting 行为一致
    const preserveNavigationData = (currentData: number[]) => {
      // 使用全局导航数据，确保数据持久化
      currentData[12] = globalNavigationData.value.byte12
      currentData[13] = globalNavigationData.value.byte13
      currentData[14] = globalNavigationData.value.byte14
      currentData[15] = globalNavigationData.value.byte15
      return currentData
    }

    try {
      // 🔧 根据 useSetting.ts 的实际映射关系重新实现

      // 索引0：P5参数（setP5）
      data[0] = Int2Bytes(p5.value)

      // 🔧 处理 c3 参数：c3 对应档位设置（与 useSetting.ts 的 setC3 逻辑一致）
      // c3 不直接写入数组，而是影响 gearPosition，进而影响索引1的计算
      // 档位与灯光状态（完全照搬 useSetting 的 updateFirstIndexOfData）
      let effectiveGearPosition = gearPosition.value
      if (c3.value === 8 && effectiveGearPosition === -1) {
        effectiveGearPosition = 5
      }
      if (c3.value !== undefined && c3.value !== 8) {
        effectiveGearPosition = Number(c3.value)
      }
      const lightValue = lightStatus.value ? LightDirection.on : LightDirection.off
      switch (effectiveGearPosition) {
        case 0:
          data[1] = GearDirection.positionZero + lightValue
          break
        case 1:
          data[1] = GearDirection.positionOne + lightValue
          break
        case 2:
          data[1] = GearDirection.positionTwo + lightValue
          break
        case 3:
          data[1] = GearDirection.positionThree + lightValue
          break
        case 4:
          data[1] = GearDirection.positionFour + lightValue
          break
        case 5:
          data[1] = GearDirection.positionFive + lightValue
          break
        default:
          data[1] = GearDirection.positionFive + lightValue
          break
      }
      
      // 索引2：速度和轮径计算（setMaxSpeed）
      const speed = Number(maxSpeed.value) - 10

      // 获取轮径信息（getDimension）
      const getDimensionInfo = () => {
        if (dimension.value < 10) {
          return {
            code: 0,
            dimension: Int2Bytes(dimension.value)
          }
        } else {
          const diff = dimension.value - 10
          return {
            code: 1,
            dimension: Int2Bytes(diff)
          }
        }
      }

      const dimensionMode = getDimensionInfo()
      let expand: number = dimensionMode.code === 1 ? 0x80 : 0x0
      let secondData = 0x0
      let limitSpeed = 0x0

      if (speed < 32) {
        limitSpeed = 0x0
        const speedBit = speed << 3
        secondData = Int2Bytes(speedBit + dimensionMode.dimension)
        data[2] = secondData
        data[4] = Int2Bytes(
                  Int2Bytes(p2.value) +
                  (Int2Bytes(p3.value) << 3) +
                  (Int2Bytes(p4.value) << 4) +
                  limitSpeed +
                  expand // p2+p3+p4+限速+轮径扩展
                )
      } else if (speed >= 32 && speed < 64) {
        limitSpeed = 0x20
        const speedDiff = speed - 32
        if (speedDiff === 0) {
          secondData = 0x5
        } else {
          const speedBit = speedDiff << 3
          secondData = Int2Bytes(speedBit + dimensionMode.dimension)
        }
        data[2] = secondData
        data[4] = Int2Bytes(
                  Int2Bytes(p2.value) +
                  (Int2Bytes(p3.value) << 3) +
                  (Int2Bytes(p4.value) << 4) +
                  limitSpeed +
                  expand // p2+p3+p4+限速+轮径扩展
                )
      }

      // 索引3：P1参数（setP1）
      data[3] = Int2Bytes(p1.value)

      // 索引6：C1C2参数（setC1C2）
      const c1Hex = Int2Bytes(c1.value)
      const c2Hex = Int2Bytes(c2.value)
      data[6] = ((c1Hex << 3) + c2Hex) & 0xFF

      // 索引7：C5C14参数（setC5C14）
      const c5Hex = Int2Bytes(c5.value)
      const c14Hex = Int2Bytes(c14.value) << 5
      data[7] = (128 + c14Hex + c5Hex) & 0xFF

      // 索引8：C4C7C12参数（setC4C7C12）
      const c4Hex = Int2Bytes(c4.value) << 5
      const c7Hex = Int2Bytes(c7.value) << 3
      const c12Hex = Int2Bytes(c12.value)
      data[8] = (c4Hex + c7Hex + c12Hex) & 0xFF

      // 索引9：把手最大速度（setHandlebar）
      data[9] = Int2Bytes(handlebarMaxSpeed.value)

      // 索引10：C13参数（setC13）
      const c13Hex = Int2Bytes(c13.value) << 2
      data[10] = (c13Hex + 1) & 0xFF

      // 索引11：百分比（setPercent）
      data[11] = Int2Bytes(percent.value)

      // 🔧 先设置导航数据，再计算校验和
      preserveNavigationData(data)

      // 校验和（索引5）
      data[5] = (data[1] ^ data[2] ^ data[3] ^ data[4] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11]) & 0xFF

      // 校验和（索引16）
      data[16] = (data[1] ^ data[2] ^ data[3] ^ data[4] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11] ^ data[12] ^ data[13] ^ data[14] ^ data[15]) & 0xFF

      // 索引17：固定值 0x0e
      data[17] = 0x0e

      // 🔧 优化：只在数据变化时输出日志，避免重复日志
      const dataStr = data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ')
      if (!lastDataStr || lastDataStr !== dataStr) {
        console.log("🔧 writeData计算完成:", dataStr)
        lastDataStr = dataStr
      }

      return data
    } catch (error) {
      console.error('❌ useBluetoothData 计算失败:', error)
      return [...WriteData]
    }
  })

  // 🔧 更新导航数据的方法
  const updateNavigationData = (nav12: number, nav13: number, nav14: number, nav15: number) => {
    // 更新全局导航数据
    globalNavigationData.value = {
      byte12: Int2Bytes(nav12),
      byte13: Int2Bytes(nav13),
      byte14: Int2Bytes(nav14),
      byte15: Int2Bytes(nav15)
    }

    // 导航数据现在通过全局状态管理，无需本地保存
    console.log('🔧 导航数据已更新:', globalNavigationData.value)
  }

  return {
    writeData,
    updateNavigationData,
    // 🔧 为兼容性暴露额外的属性
    currentSendData: computed(() => writeData.value),
    Int2Bytes,
    calculateChecksum: (data: number[]) => {
      // 简单的校验和计算（可以根据需要调整）
      return data.reduce((sum, byte) => sum ^ byte, 0) & 0xFF
    }
  }
}
