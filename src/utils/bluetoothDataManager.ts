import { isPlatform } from '@ionic/vue';
import { ref } from 'vue';

/**
 * 🔧 简化版蓝牙数据管理器
 * 专门用于设置页面的数据更新，避免复杂的实例管理
 */
export class BluetoothDataManager {
  private static instance: BluetoothDataManager | null = null;

  // 数据更新状态
  private isUpdating = ref(false);
  private lastUpdateTime = ref(0);

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): BluetoothDataManager {
    if (!BluetoothDataManager.instance) {
      BluetoothDataManager.instance = new BluetoothDataManager();
    }
    return BluetoothDataManager.instance;
  }
  
  /**
   * 🔧 简化版：更新设置并发送蓝牙数据
   * 直接调用智能蓝牙方案，避免复杂的实例管理
   */
  public async updateSettingsAndSend(): Promise<boolean> {
    // 防止重复更新
    if (this.isUpdating.value) {
      console.log("⚠️ 设置更新正在进行中，跳过重复调用");
      return false;
    }

    this.isUpdating.value = true;

    try {
      console.log("🔧 开始更新设置并发送蓝牙数据");

      // 🎯 简化：只更新数据，不启动定时器
      // 定时器应该在应用启动时就开始运行，这里只需要更新数据内容
      const { useBluetoothData } = await import("@/composables/useBluetoothData");
      const { writeData } = useBluetoothData();

      // 触发数据重新计算（响应式会自动更新writeData）
      console.log("🔧 数据已更新:", writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));

      this.lastUpdateTime.value = Date.now();
      console.log("✅ 设置更新并发送完成");

      // 发送通知事件
      this.notifyDataUpdated('settings-saved');

      return true;

    } catch (error) {
      console.error("❌ 更新设置并发送失败:", error);
      return false;
    } finally {
      this.isUpdating.value = false;
    }
  }

  /**
   * 发送数据更新完成通知
   * @param reason 更新原因
   */
  private notifyDataUpdated(reason: string) {
    console.log(`📢 蓝牙数据更新完成通知: ${reason}`);

    window.dispatchEvent(new CustomEvent('bluetoothDataUpdated', {
      detail: {
        timestamp: this.lastUpdateTime.value,
        source: 'BluetoothDataManager',
        reason: reason
      }
    }));
  }

  /**
   * 获取更新状态
   */
  public getUpdateStatus() {
    return {
      isUpdating: this.isUpdating.value,
      lastUpdateTime: this.lastUpdateTime.value
    };
  }

  /**
   * 重置管理器状态
   */
  public reset() {
    this.isUpdating.value = false;
    this.lastUpdateTime.value = 0;
    console.log("🧹 蓝牙数据管理器状态已重置");
  }

  /**
   * 销毁管理器实例
   */
  public static destroy() {
    if (BluetoothDataManager.instance) {
      BluetoothDataManager.instance.reset();
      BluetoothDataManager.instance = null;
      console.log("🗑️ 蓝牙数据管理器实例已销毁");
    }
  }
}

/**
 * 导出单例实例
 */
export const bluetoothDataManager = BluetoothDataManager.getInstance();
