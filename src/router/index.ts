import { createRouter, createWebHistory } from '@ionic/vue-router'
import { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/tabs/home'
  },
  {
    path: '/tabs/',
    component: () => import('@/views/TabsPage.vue'),
    children: [
      {
        path: '',
        redirect: '/tabs/home'
      },
      {
        path: 'home',
        component: () => import('@/views/HomePage.vue')
      },
      {
        path: 'my',
        component: () => import('@/views/MyPage.vue')
      },
      {
        path: 'navigation',
        component: () => import('@/views/navigation/MapboxSearchPage.vue')
      },
      {
        path: 'mapbox',
        component: () => import('@/views/MapBoxPage.vue')
      }
    ]
  },
  {
    name: 'setting',
    path: '/setting',
    component: () => import('@/views/SettingPage.vue')
  },
  {
    name: 'help',
    path: '/help',
    component: () => import('@/views/HelpPage.vue')
  },
  {
    name: 'bluetooth',
    path: '/bluetooth',
    component: () => import('@/views/BluetoothPage.vue')
  },
  {
    name: 'history',
    path: '/history',
    component: () => import('@/views/HistoryPage.vue')
  },
  {
    name: 'bluetooth-comparison',
    path: '/bluetooth-comparison',
    component: () => import('@/views/BluetoothDataComparisonPage.vue')
  },
  {
    name: 'navigation-control',
    path: '/navigation-control',
    component: () => import('@/views/NavigationControlDemo.vue')
  },
  {
    name: 'mapbox-search-test',
    path: '/mapbox-search-test',
    component: () => import('@/views/MapboxSearchTestPage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
