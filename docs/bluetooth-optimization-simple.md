# 蓝牙自动连接优化 - 简化版

## 问题
应用启动时蓝牙自动连接和发送数据失败，根本原因：
- `❌ Bluetooth LE not initialized.` - 蓝牙LE还没初始化就开始检查状态
- `❌ 启动蓝牙前台服务失败` - 时序问题导致服务未准备好

## 解决方案

### 1. 修复蓝牙LE初始化时序

**核心问题**: 蓝牙LE还没初始化就开始检查状态和发送数据

**解决方案**:
- 在所有蓝牙操作前确保 `BleClient.initialize()` 已完成
- 使用主动等待而不是固定延迟
- 添加重试机制和超时保护

```typescript
// 智能等待蓝牙初始化完成
const waitForBluetoothInitAndStart = async () => {
  const maxWaitTime = 15000; // 最大等待15秒
  const checkInterval = 500; // 每500ms检查一次

  const checkAndStart = async () => {
    try {
      await initialBle(); // 尝试初始化
      await startSending(); // 启动发送服务
    } catch (error) {
      // 初始化未完成，继续等待
      setTimeout(checkAndStart, checkInterval);
    }
  };

  checkAndStart();
};
```

### 2. 修复状态检查函数

**文件**: `src/hooks/useNativeBluetoothMessage.ts`

**改进**: 在检查蓝牙状态前先初始化

```typescript
const checkBluetoothStatus = async () => {
  // 先确保蓝牙LE已初始化
  await BluetoothLe.initialize();
  // 再检查状态
  const isEnabled = await BluetoothLe.isEnabled();
  return isEnabled.value;
};
```

### 3. 优化连接监听器

**文件**: `src/composables/useBluetoothManager.ts`

**改进**: 连接状态变化时也确保初始化

```typescript
watch(() => connectedDevice.value?.isPaired, async (isConnected) => {
  if (isConnected) {
    // 确保蓝牙LE已初始化
    await initialBle();
    await bluetoothSender.startSending();
  }
});
```

## 核心改动

### useBluetoothAutoSender.ts

1. **智能等待**: 新增 `waitForBluetoothInitAndStart` 函数，主动等待初始化完成
2. **重试机制**: `checkAndRestoreBluetoothSending` 添加重试逻辑（最多5次）
3. **超时保护**: 最大等待15秒，避免无限等待

### useNativeBluetoothMessage.ts

1. **状态检查**: `checkBluetoothStatus` 添加 `BluetoothLe.initialize()`
2. **错误处理**: 更好的初始化错误处理

### useBluetoothManager.ts

1. **智能监听**: 连接状态变化时主动等待初始化完成
2. **重试机制**: 最多重试10次，每次间隔1秒
3. **连接稳定**: 500ms 延迟确保连接稳定后再尝试初始化

## 测试要点

1. **应用启动**: 检查是否能正常自动连接和发送数据
2. **手动重连**: 断开后手动连接是否正常
3. **页面切换**: 进入页面时状态检查是否正常
4. **错误恢复**: 连接失败后是否能正常恢复

## 优势

1. **简洁**: 移除过度设计，保持代码简单
2. **稳定**: 增加适当延迟确保时序正确
3. **维护**: 易于理解和维护
4. **兼容**: 保持现有架构不变

## 使用方式

无需改变现有使用方式，优化是透明的：

```typescript
// 页面中继续使用原有方式
const { checkAndRestoreBluetoothSending } = useBluetoothAutoSender();

onIonViewDidEnter(async () => {
  await checkAndRestoreBluetoothSending();
});
```

这个优化专注解决核心问题：**启动时序**，通过适当的延迟确保蓝牙服务完全初始化后再尝试连接和发送数据。
