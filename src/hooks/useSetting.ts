import { useSettingStore } from '@/store/useSettingStore'
import { useDashboardStore } from '@/store/useDashboardStore'
import { GearDirection, LightDirection } from '@/const/bike.const'
import { WriteData } from '@/const/ble.const'
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { globalDiagnostics } from '@/utils/runtimeDiagnostics'

// 添加可选的回调函数参数来打破循环依赖
export function useSetting(onDataUpdate?: () => void) {
  const writeData = ref([...WriteData]) // 使用展开运算符创建新数组，避免引用问题
  const dashboardStore = useDashboardStore()
  /*从store里获取设置数据*/
  const {
    dimension,
    maxSpeed,
    p1,
    p2,
    p3,
    p4,
    p5,
    c1,
    c2,
    c3,
    c4,
    c5,
    c7,
    c12,
    c13,
    c14,
    percent,
    handlebarMaxSpeed
  } = storeToRefs(useSettingStore())
  const { gearPosition, lightStatus } = storeToRefs(dashboardStore)
  const { setGearPosition, setLightStatus } = useDashboardStore()

  /*设置最大速度*/
  const setMaxSpeed = () => {
    const speed = Number(maxSpeed.value) - 10
    const dimensionMode = getDimension()
    let expand: number
    let secondData = 0x0
    let limitSpeed = 0x0
    if (dimensionMode.code === 1) {
      expand = 0x80
    } else {
      expand = 0x0
    }
    if (speed < 32) {
      limitSpeed = 0x0
      const speedBit: number = speed << 3
      secondData = Int2Bytes(speedBit + dimensionMode.dimension)
      writeData.value[2] = secondData
      writeData.value[4] =
        Int2Bytes(p2.value) +
        (Int2Bytes(p3.value) << 3) +
        (Int2Bytes(p4.value) << 4) +
        limitSpeed +
        expand // p2+p3+p4+限速+轮径拓展
    }
    if (speed >= 32 && speed < 64) {
      limitSpeed = 0x20
      const speedDiff = speed - 32
      if (speedDiff === 0) {
        secondData = 0x5
      } else {
        const speedBit: number = speedDiff << 3
        secondData = Int2Bytes(speedBit + dimensionMode.dimension)
      }
      writeData.value[2] = secondData
      writeData.value[4] =
        Int2Bytes(p2.value) +
        (Int2Bytes(p3.value) << 3) +
        (Int2Bytes(p4.value) << 4) +
        limitSpeed +
        expand // p2+p3+p4+限速+轮径拓展
    }
    updateFiveIndexOfData()
    // code = 0 轮径不需要扩展  code = 1 需要扩展
    // maxSpeed < 31 限速不需要扩展
  }

  /*获取不同尺寸轮径的数据*/
  const getDimension = () => {
    if (dimension.value < 10) {
      return {
        code: 0,
        dimension: Int2Bytes(dimension.value)
      }
    } else {
      const diff = dimension.value - 10
      return {
        code: 1,
        dimension: Int2Bytes(diff)
      }
    }
  }

  const changeGearPosition = (position: number) => {
    setGearPosition(position)
    updateFirstIndexOfData()
  }
  const changeLightStatus = (status: boolean) => {
    setLightStatus(status)
    updateFirstIndexOfData()
  }
  const updateFirstIndexOfData = () => {
    // 档位与灯光状态
    let effectiveGearPosition = gearPosition.value
    if (c3.value === 8 && effectiveGearPosition === -1) {
      effectiveGearPosition = 5
    }
    if (c3.value !== undefined && c3.value !== 8) {
      effectiveGearPosition = Number(c3.value)
    }
    const lightValue = lightStatus.value
      ? LightDirection.on
      : LightDirection.off
    switch (effectiveGearPosition) {
      case 0:
        writeData.value[1] = GearDirection.positionZero + lightValue
        break
      case 1:
        writeData.value[1] = GearDirection.positionOne + lightValue
        break
      case 2:
        writeData.value[1] = GearDirection.positionTwo + lightValue
        break
      case 3:
        writeData.value[1] = GearDirection.positionThree + lightValue
        break
      case 4:
        writeData.value[1] = GearDirection.positionFour + lightValue
        break
      case 5:
        writeData.value[1] = GearDirection.positionFive + lightValue
        break
      default:
        writeData.value[1] = GearDirection.positionFive + lightValue
        break
    }
    updateFiveIndexOfData()
  }

  const updateFiveIndexOfData = () => {
    writeData.value[5] =
      (
        writeData.value[1] ^
        writeData.value[2] ^
        writeData.value[3] ^
        writeData.value[4] ^
        writeData.value[6] ^
        writeData.value[7] ^
        writeData.value[8] ^
        writeData.value[9] ^
        writeData.value[10] ^
        writeData.value[11]
      ) & 0xFF

    validateSixteen()

    // 🔍 记录数据变化到诊断日志
    if (globalDiagnostics.isMonitoring.value) {
      globalDiagnostics.logDiagnostic('useSetting', 'updateFiveIndexOfData', writeData.value);
    }
  }

  const validateSixteen = () => {
    writeData.value[16] =
      (
        writeData.value[1] ^
        writeData.value[2] ^
        writeData.value[3] ^
        writeData.value[4] ^
        writeData.value[6] ^
        writeData.value[7] ^
        writeData.value[8] ^
        writeData.value[9] ^
        writeData.value[10] ^
        writeData.value[11] ^
        writeData.value[12] ^
        writeData.value[13] ^
        writeData.value[14] ^
        writeData.value[15]
      ) & 0xFF
  }

  const setP1 = () => {
    writeData.value[3] = Int2Bytes(p1.value)
    updateFiveIndexOfData()
  }
  const setP2 = () => {
    // writeData.value[4] = Int2Bytes(p2);
    setMaxSpeed()
  }
  const setP3 = () => {
    // writeData.value[4] = Int2Bytes(p3) << 3 || writeData.value[4]
    setMaxSpeed()
  }
  const setP4 = () => {
    // writeData.value[4] = Int2Bytes(p4) << 4 || writeData.value[4]
    setMaxSpeed()
  }
  const setP5 = () => {
    writeData.value[0] = Int2Bytes(p5.value)
    updateFiveIndexOfData()
  }
  const setC1C2 = () => {
    const c1Hex = Int2Bytes(c1.value)
    const c2Hex = Int2Bytes(c2.value)
    writeData.value[6] = ((c1Hex << 3) + c2Hex) & 0xFF
    updateFiveIndexOfData()
  }
  // c3 对应的是档位
  const setC3 = () => {
    const position = Number(c3.value)
    if (position === 8) return
    gearPosition.value = Number(position)
    changeGearPosition(position)
  }
  const setC5C14 = () => {
    const c5Hex = Int2Bytes(c5.value)
    const c14Hex = Int2Bytes(c14.value) << 5
    writeData.value[7] = (128 + c14Hex + c5Hex) & 0xFF
    updateFiveIndexOfData()
  }
  const setC4C7C12 = () => {
    const c4Hex = Int2Bytes(c4.value) << 5
    const c7Hex = Int2Bytes(c7.value) << 3
    const c12Hex = Int2Bytes(c12.value)
    writeData.value[8] = (c4Hex + c7Hex + c12Hex) & 0xFF
    updateFiveIndexOfData()
  }
  const setC13 = () => {
    const c13Hex = Int2Bytes(c13.value) << 2
    writeData.value[10] = (c13Hex + 1) & 0xFF
    updateFiveIndexOfData()
  }
  const setPercent = () => {
    writeData.value[11] = Int2Bytes(percent.value)
    updateFiveIndexOfData()
  }
  const setHandlebar = () => {
    writeData.value[9] = Int2Bytes(handlebarMaxSpeed.value)
    updateFiveIndexOfData()
  }
  const Int2Bytes = (value: number | string) => {
    try {
      if (typeof value === 'string') {
        value = Number(value)
      }
      
      // 确保value是有效数字
      if (isNaN(value) || value === null || value === undefined) {
        console.warn(`🔧 Int2Bytes: 无效值 ${value}，使用默认值0`);
        return 0;
      }
      
      // 简化版本：直接使用位运算，因为所有应用中的值都在0-255范围内
      // 测试证明：原始复杂的16进制转换等同于 value & 0xFF
      return value & 0xFF;
      
    } catch (error) {
      console.error(`🔧 Int2Bytes: 转换失败 ${value}`, error);
      return 0;
    }
  }

  const HexString2Bytes = (hexString: string) => {
    try {
      if (!hexString || typeof hexString !== 'string') {
        console.warn(`🔧 HexString2Bytes: 无效的hexString ${hexString}，返回[0]`);
        return [0];
      }
      
      let pos = 0
      let len = hexString.length
      
      // 处理奇数长度的hexString
      if (len % 2 !== 0) {
        hexString = '0' + hexString; // 在前面补0
        len = hexString.length;
      }
      
      len /= 2
      const arrBytes = []
      for (let i = 0; i < len; i++) {
        const s = hexString.substring(pos, pos + 2)
        const v = parseInt(s, 16)
        
        // 确保解析结果是有效数字
        if (isNaN(v)) {
          console.warn(`🔧 HexString2Bytes: 解析失败 ${s}，使用0`);
          arrBytes.push(0);
        } else {
          arrBytes.push(v);
        }
        pos += 2
      }
      
      // 确保至少返回一个元素
      if (arrBytes.length === 0) {
        console.warn(`🔧 HexString2Bytes: 结果为空，返回[0]`);
        return [0];
      }
      
      return arrBytes;
    } catch (error) {
      console.error(`🔧 HexString2Bytes: 转换失败 ${hexString}`, error);
      return [0];
    }
  }
  const validateWriteData = () => {
    console.log("🔧 开始验证writeData数据完整性");
    for (let i = 0; i < writeData.value.length; i++) {
      const value = writeData.value[i];
      if (value === null || value === undefined || isNaN(value)) {
        console.warn(`🔧 writeData[${i}] 为无效值 ${value}，设置为0`);
        writeData.value[i] = 0;
      } else if (value < 0 || value > 255) {
        console.warn(`🔧 writeData[${i}] 值 ${value} 超出范围[0-255]，调整为${Math.max(0, Math.min(255, value))}`);
        writeData.value[i] = Math.max(0, Math.min(255, Math.floor(value)));
      }
    }
    console.log("🔧 writeData验证完成:", writeData.value);
  }

  const updateSetting = () => {
    console.log("🔧 updateSetting - 开始更新设置");
    console.log("🔧 更新前writeData:", writeData.value);
    
    // 🔧 保存当前的导航数据（字节12-15），避免在设置更新时被覆盖
    const currentNavigationData = {
      byte12: writeData.value[12],
      byte13: writeData.value[13],
      byte14: writeData.value[14],
      byte15: writeData.value[15],
      byte16: writeData.value[16] // 校验和也需要保存
    };
    console.log("🔧 保存当前导航数据:", currentNavigationData);
    
    // 🔧 记录更新前的关键设置值
    console.log("🔧 当前设置值:", {
      maxSpeed: maxSpeed.value,
      dimension: dimension.value,
      p1: p1.value,
      p2: p2.value,
      p3: p3.value,
      p4: p4.value,
      p5: p5.value,
      c1: c1.value,
      c2: c2.value,
      c3: c3.value,
      c4: c4.value,
      c5: c5.value,
      c7: c7.value,
      c12: c12.value,
      c13: c13.value,
      c14: c14.value,
      percent: percent.value,
      handlebarMaxSpeed: handlebarMaxSpeed.value
    });
    
    // 按顺序更新各个设置参数
    console.log("🔧 开始逐步更新设置参数");
    
    console.log("🔧 更新P5 (电池)");
    setP5()
    
    console.log("🔧 更新P1 (电机设置)");
    setP1()
    
    console.log("🔧 更新C1C2 (PAS + 电机相位)");
    setC1C2()
    
    console.log("🔧 更新C3 (初始档位)");
    setC3() // 只在c3!==8时才会修改gearPosition
    
    console.log("🔧 更新档位和灯光状态");
    updateFirstIndexOfData()
    
    console.log("🔧 更新C5C14 (电流 + PAS功率)");
    setC5C14()
    
    console.log("🔧 更新C4C7C12 (油门 + 巡航 + 欠压)");
    setC4C7C12()
    
    console.log("🔧 更新C13 (再生制动)");
    setC13()
    
    console.log("🔧 更新百分比 (油门第一档百分比)");
    setPercent()
    
    console.log("🔧 更新把手最大速度");
    setHandlebar()
    
    console.log("🔧 更新最大速度 (包含P2, P3, P4)");
    setMaxSpeed() // 这个方法内部会调用setP2, setP3, setP4
    
    // 🔧 恢复导航数据（字节12-15），确保设置更新不会清除导航信息
    // 注意：只有在导航数据看起来有效时才恢复（避免恢复全零数据）
    const hasValidNavigationData = currentNavigationData.byte12 !== 0 || 
                                   currentNavigationData.byte13 !== 0 || 
                                   currentNavigationData.byte14 !== 0 || 
                                   currentNavigationData.byte15 !== 0;
    
    if (hasValidNavigationData) {
      writeData.value[12] = currentNavigationData.byte12;
      writeData.value[13] = currentNavigationData.byte13;
      writeData.value[14] = currentNavigationData.byte14;
      writeData.value[15] = currentNavigationData.byte15;
      // 不恢复校验和，因为设置数据可能已经改变，需要重新计算
      console.log("🔧 已恢复导航数据到writeData");
    } else {
      console.log("🔧 当前导航数据为空，不进行恢复");
    }
    
    // 验证数据完整性（这会重新计算校验和）
    console.log("🔧 验证数据完整性");
    validateWriteData();
    
    console.log("🔧 更新后writeData:", writeData.value);
    console.log("🔧 writeData长度:", writeData.value?.length);
    console.log(`🔧 最终导航数据 - 字节12: 0x${writeData.value[12].toString(16).padStart(2, '0').toUpperCase()}, 字节13: 0x${writeData.value[13].toString(16).padStart(2, '0').toUpperCase()}, 字节14: 0x${writeData.value[14].toString(16).padStart(2, '0').toUpperCase()}, 字节15: 0x${writeData.value[15].toString(16).padStart(2, '0').toUpperCase()}`);
    
    // 🔧 显示完整的数据对比
    console.log("🔧 完整writeData十六进制表示:");
    const hexString = writeData.value.map(byte => `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
    console.log(hexString);
    
    // 🔍 记录最终的设置数据到诊断日志
    if (globalDiagnostics.isMonitoring.value) {
      globalDiagnostics.logDiagnostic('useSetting', 'updateSetting-final', writeData.value, '所有设置参数更新完成');
    }

    // 🔧 通知数据更新回调（如果存在）
    if (onDataUpdate) {
      try {
        console.log("🔧 调用数据更新回调");
        onDataUpdate();
      } catch (error) {
        console.warn('❌ 数据更新回调执行失败:', error);
      }
    } else {
      console.log("🔧 无数据更新回调，跳过通知");
    }

    console.log("✅ updateSetting - 设置更新完成");
  }

  onMounted(() => {
    console.log("🔧 useSetting onMounted - 初始化writeData:", writeData.value);
    console.log("🔧 writeData长度:", writeData.value?.length);
    updateSetting()
    console.log("🔧 useSetting onMounted - 更新后writeData:", writeData.value);
  })

  return {
    changeGearPosition,
    changeLightStatus,
    setMaxSpeed,
    writeData,
    updateSetting
  }
}
