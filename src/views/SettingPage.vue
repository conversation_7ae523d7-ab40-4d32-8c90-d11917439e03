<template>
  <ion-page class="page-setting">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/my"></ion-back-button>
        </ion-buttons>
        <ion-title>Setting</ion-title>
        <ion-buttons slot="end">
          <ion-button fill="solid" @click="saveSettings">Save</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-list>
        <ion-list-header>
          <ion-label>Parameters</ion-label>
        </ion-list-header>

        <ion-item>
          <ion-input v-model="maxSpeed" :max="72" :maxlength="2" :min="10" inputmode="numeric" label="MaxSpeed"
            name="maxSpeed" placeholder="km/h" type="number">
          </ion-input>
        </ion-item>

        <modal-select
          v-model="dimension"
          :list="dimensionList"
          modal-name="dimension"
          label="Bike Dimension"
          placeholder="Select Dimension"
        />

        <ion-item>
          <ion-input v-model="p1" inputmode="numeric" label="MotorSetting (P1)" name="p1" placeholder="P1"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p2" inputmode="numeric" label="SpeedSensor (P2)" name="p2" placeholder="P2"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p3" inputmode="numeric" label="Torque (P3)" name="p3" placeholder="P3"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p4" inputmode="numeric" label="ZeroStart (P4)" name="p4" placeholder="P4"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p5" inputmode="numeric" label="Battery (P5)" name="p5" placeholder="P5"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c1" inputmode="numeric" label="PAS (C1)" name="c1" placeholder="C1"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c2" inputmode="numeric" label="MotorPhase (C2)" name="c2" placeholder="C2"
            type="number"></ion-input>
        </ion-item>

        <!-- <ion-item>
          <ion-select v-model="c3" :toggle-icon="chevronForwardOutline" :multiple="false" label="InitLevel (C3)"
            name="initLevel" placeholder="Select InitLevel">
            <ion-select-option v-for="(item, index) of levelList" :key="index" :value="item.value">
              {{ item.name }}
            </ion-select-option>
          </ion-select>
        </ion-item> -->
        <modal-select
          v-model="c3"
          :list="levelList"
          modal-name="initLevel"
          label="InitLevel (C3)"
          placeholder="Select InitLevel"
        />
        
        <ion-item>
          <ion-input v-model="c4" inputmode="numeric" label="Throttle (C4)" name="c4" placeholder="C4"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c5" inputmode="numeric" label="Current (C5)" name="c5" placeholder="C5"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c7" inputmode="numeric" label="Cruise (C7)" name="c7" placeholder="C7"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c12" inputmode="numeric" label="UVLO (C12)" name="c12" placeholder="C12"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c13" inputmode="numeric" label="Regenerative (C13)" name="c13" placeholder="C13"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c14" inputmode="numeric" label="PASPower (C14)" name="c14" placeholder="C14"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="handlebarMaxSpeed" inputmode="numeric" label="Handlebar Maximum Speed"
            name="handleBarSpeed" placeholder="data.displayType === 'kilometer' ? 'km/h' : 'mile/h'"
            type="number"></ion-input>
          <!--TODO  c4必须等于2，才会起作用        -->
        </ion-item>

        <ion-item>
          <ion-input v-model="percent" inputmode="numeric" label="FirstLevelPercent (Throttle)" name="percent"
            placeholder="percentage" type="number"></ion-input>
          <!--TODO  c4必须等于4，才会起作用        -->
        </ion-item>

        <ion-item>
          <ion-input v-model="candidateParam" inputmode="numeric" label="Candidate" name="candidate"
            placeholder="Signal of PAS" type="number"></ion-input>
        </ion-item>

        <ion-radio-group v-model="displayType" name="displayType">
          <ion-list-header>Kilometer／Mile</ion-list-header>
          <ion-item>
            <ion-radio value="kilometer">Kilometer</ion-radio>
          </ion-item>
          <ion-item>
            <ion-radio value="mile">Mile</ion-radio>
          </ion-item>
        </ion-radio-group>
      </ion-list>
      <ion-grid>
        <ion-row>
          <ion-col class="page-setting__restore">
            <ion-button fill="outline" shape="round" size="small" @click="restore">Restore Settings
            </ion-button>
          </ion-col>
        </ion-row>
        <ion-row v-if="isDevelopment">
          <ion-col class="page-setting__test">
            <ion-button fill="solid" shape="round" size="small" color="secondary" @click="runBluetoothTest">
              Test Bluetooth Data Manager
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>

    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonButton,
  IonButtons,
  IonBackButton,
  IonCol,
  IonContent,
  IonGrid,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonListHeader,
  IonPage,
  IonRadio,
  IonRadioGroup,
  IonRow,
  IonSelect,
  IonSelectOption,
  IonTitle,
  IonToolbar,
  onIonViewWillEnter,
  onIonViewWillLeave,
} from "@ionic/vue";
import { useSettingStore } from "@/store/useSettingStore";
import { useErrorStore } from "@/store/useErrorStore";
import { bluetoothDataManager } from "@/utils/bluetoothDataManager";
import { testBluetoothDataManager } from "@/utils/bluetoothDataManagerTest";
import { storeToRefs } from "pinia";
import { chevronForwardOutline } from "ionicons/icons";
import ModalSelect from '@/components/ModalSelect.vue'

const settingStore = useSettingStore();
const errorStore = useErrorStore();
const {
  maxSpeed,
  dimension,
  dimensionList,
  p1,
  p2,
  p3,
  p4,
  p5,
  c1,
  c2,
  c3,
  c4,
  c5,
  c7,
  c12,
  c13,
  c14,
  levelList,
  handlebarMaxSpeed,
  percent,
  candidateParam,
  displayType,
} = storeToRefs(settingStore);

// 开发环境检测
const isDevelopment = import.meta.env.DEV;





// onIonViewWillLeave(async () => {
//   console.log("🔧 SettingPage - 页面即将离开，更新设置并发送蓝牙数据");

//   try {
//     // 使用统一的蓝牙数据管理器更新设置并发送
//     const updateSuccess = await bluetoothDataManager.updateSettingsAndSend();

//     if (updateSuccess) {
//       console.log("✅ 页面离开时设置更新并发送完成");
//     } else {
//       console.warn("⚠️ 页面离开时设置更新失败");
//     }
//   } catch (error) {
//     console.error("❌ 页面离开时设置更新失败:", error);
//   }
// });

const saveSettings = async () => {
  console.log("🔧 SettingPage - 手动保存设置");

  try {
    // 使用统一的蓝牙数据管理器更新设置并发送
    const updateSuccess = await bluetoothDataManager.updateSettingsAndSend();

    if (updateSuccess) {
      // 显示成功提示
      const { useToast } = await import("@/hooks/useToast");
      const { presentToast } = useToast();
      await presentToast("设置已保存并同步到设备");

      console.log("✅ 设置保存完成");
    } else {
      throw new Error("设置更新并发送失败");
    }

  } catch (error) {
    console.error("❌ 设置保存失败:", error);

    // 显示错误提示
    try {
      const { useToast } = await import("@/hooks/useToast");
      const { presentToast } = useToast();
      await presentToast("设置保存失败，请重试");
    } catch (toastError) {
      console.error("❌ 显示错误提示失败:", toastError);
    }
  }
};

const restore = () => {
  settingStore.$reset();
  errorStore.$reset();
};

const runBluetoothTest = async () => {
  console.log("🧪 开始运行蓝牙数据管理器测试");

  try {
    // 显示开始提示
    const { useToast } = await import("@/hooks/useToast");
    const { presentToast } = useToast();
    await presentToast("开始测试蓝牙数据管理器...");

    // 运行测试套件
    await testBluetoothDataManager();

    await presentToast("测试完成，请查看控制台日志");

  } catch (error) {
    console.error("❌ 测试失败:", error);

    try {
      const { useToast } = await import("@/hooks/useToast");
      const { presentToast } = useToast();
      await presentToast("测试失败，请查看控制台");
    } catch (toastError) {
      console.error("❌ 显示错误提示失败:", toastError);
    }
  }
};
</script>
<style lang="scss">
.page-setting {
  ion-list-header {
    --color: var(--ion-color-primary);
  }

  ion-button:not(.button-round) {
    --border-radius: 8px;
    --padding-start: 12px;
    --padding-end: 12px;
  }

  ion-input {
    text-align: right;
  }

  .page-setting__restore {
    display: flex;
    justify-content: center;
  }

  .page-setting__test {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }
}
</style>
