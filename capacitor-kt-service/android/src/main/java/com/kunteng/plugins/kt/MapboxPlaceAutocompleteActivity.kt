package com.kunteng.plugins.kt

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.Style
import com.mapbox.maps.plugin.annotation.annotations
import com.mapbox.maps.plugin.annotation.generated.CircleAnnotationOptions
import com.mapbox.maps.plugin.annotation.generated.createCircleAnnotationManager
import com.mapbox.maps.plugin.gestures.addOnMapLongClickListener
import com.mapbox.maps.plugin.locationcomponent.OnIndicatorPositionChangedListener
import com.mapbox.maps.plugin.locationcomponent.location
import com.mapbox.search.autocomplete.PlaceAutocomplete
import com.mapbox.search.autocomplete.PlaceAutocompleteOptions
import com.mapbox.search.autocomplete.PlaceAutocompleteSuggestion
import com.mapbox.search.autocomplete.PlaceAutocompleteType

import com.mapbox.search.result.SearchAddress
import com.mapbox.search.ui.adapter.autocomplete.PlaceAutocompleteUiAdapter
import com.mapbox.search.ui.view.CommonSearchViewConfiguration
import com.mapbox.search.ui.view.SearchResultsView
import com.mapbox.search.ui.view.place.SearchPlace
import com.mapbox.search.ui.view.place.SearchPlaceBottomSheetView
import com.mapbox.common.MapboxOptions
import kotlinx.coroutines.launch

class MapboxPlaceAutocompleteActivity : AppCompatActivity() {

    companion object {
        const val RESULT_EXTRA_NAME = "result_name"
        const val RESULT_EXTRA_PLACE_NAME = "result_name" // 为了兼容性
        const val RESULT_EXTRA_ADDRESS = "result_address"
        const val RESULT_EXTRA_LATITUDE = "result_latitude"
        const val RESULT_EXTRA_LONGITUDE = "result_longitude"

        private const val PERMISSIONS_REQUEST_LOCATION = 0
        private const val LOG_TAG = "PlaceAutocompleteActivity"

        private val MARKERS_EDGE_OFFSET = dpToPx(64).toDouble()
        private val PLACE_CARD_HEIGHT = dpToPx(300).toDouble()
        private val MARKERS_TOP_OFFSET = dpToPx(88).toDouble()
        private val MARKERS_INSETS_OPEN_CARD = EdgeInsets(
            MARKERS_TOP_OFFSET, MARKERS_EDGE_OFFSET, PLACE_CARD_HEIGHT, MARKERS_EDGE_OFFSET
        )

        private val REGION_LEVEL_TYPES = listOf(
            PlaceAutocompleteType.AdministrativeUnit.Country,
            PlaceAutocompleteType.AdministrativeUnit.Region
        )

        private val DISTRICT_LEVEL_TYPES = REGION_LEVEL_TYPES + listOf(
            PlaceAutocompleteType.AdministrativeUnit.Postcode,
            PlaceAutocompleteType.AdministrativeUnit.District
        )

        private val LOCALITY_LEVEL_TYPES = DISTRICT_LEVEL_TYPES + listOf(
            PlaceAutocompleteType.AdministrativeUnit.Place,
            PlaceAutocompleteType.AdministrativeUnit.Locality
        )

        private val ALL_TYPES = listOf(
            PlaceAutocompleteType.Poi,
            PlaceAutocompleteType.AdministrativeUnit.Country,
            PlaceAutocompleteType.AdministrativeUnit.Region,
            PlaceAutocompleteType.AdministrativeUnit.Postcode,
            PlaceAutocompleteType.AdministrativeUnit.District,
            PlaceAutocompleteType.AdministrativeUnit.Place,
            PlaceAutocompleteType.AdministrativeUnit.Locality,
            PlaceAutocompleteType.AdministrativeUnit.Neighborhood,
            PlaceAutocompleteType.AdministrativeUnit.Street,
            PlaceAutocompleteType.AdministrativeUnit.Address,
        )

        private fun dpToPx(dp: Int): Int = (dp * Resources.getSystem().displayMetrics.density).toInt()
    }

    private lateinit var placeAutocomplete: PlaceAutocomplete
    private lateinit var searchResultsView: SearchResultsView
    private lateinit var placeAutocompleteUiAdapter: PlaceAutocompleteUiAdapter
    private lateinit var queryEditText: EditText
    private lateinit var mapView: MapView
    private lateinit var mapboxMap: MapboxMap
    private lateinit var mapMarkersManager: MapMarkersManager

    // 自定义地点卡片相关视图
    private lateinit var customPlaceCard: View
    private lateinit var placeNameText: TextView
    private lateinit var placeAddressText: TextView
    private lateinit var distanceText: TextView
    private lateinit var goHereButton: View

    // 当前选中的地点
    private var currentSelectedPlace: SearchPlace? = null

    private var ignoreNextQueryUpdate = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置透明状态栏
        setupTransparentStatusBar()

        val layoutId = getAutocompleteLayoutId()
        if (layoutId == 0) {
            Toast.makeText(this, "Autocomplete layout not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setContentView(layoutId)

        // 设置 Mapbox Access Token
        val accessToken = getMapboxAccessToken()
        if (accessToken.isNullOrEmpty()) {
            Toast.makeText(this, "Mapbox access token not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        MapboxOptions.accessToken = accessToken

        // 创建 PlaceAutocomplete 实例
        placeAutocomplete = PlaceAutocomplete.create()

        initializeViews()
        setupMap()
        setupPlaceAutocompleteAdapter()
        setupTextWatcher()

        // 请求位置权限
        if (!isPermissionGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ),
                PERMISSIONS_REQUEST_LOCATION
            )
        }
    }

    private fun setupTransparentStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.apply {
                clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
                addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                statusBarColor = Color.TRANSPARENT
                navigationBarColor = Color.TRANSPARENT

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    // 设置状态栏文字为黑色（深色内容），地图延伸到导航栏
                    decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                                   View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                                   View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR or
                                                   View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                }
            }
        }
    }

    private fun getAutocompleteLayoutId(): Int {
        // 动态获取布局资源ID
        return resources.getIdentifier("activity_mapbox_place_autocomplete", "layout", packageName)
    }

    private fun initializeViews() {
        queryEditText = findViewById(resources.getIdentifier("query_text", "id", packageName))
        mapView = findViewById(resources.getIdentifier("map_view", "id", packageName))
        searchResultsView = findViewById(resources.getIdentifier("search_results_view", "id", packageName))

        // 初始化自定义地点卡片
        customPlaceCard = findViewById(resources.getIdentifier("custom_place_card", "id", packageName))
        placeNameText = customPlaceCard.findViewById(resources.getIdentifier("place_name", "id", packageName))
        placeAddressText = customPlaceCard.findViewById(resources.getIdentifier("place_address", "id", packageName))
        distanceText = customPlaceCard.findViewById(resources.getIdentifier("distance_text", "id", packageName))
        goHereButton = customPlaceCard.findViewById(resources.getIdentifier("go_here_button", "id", packageName))

        if (!::queryEditText.isInitialized || !::mapView.isInitialized ||
            !::searchResultsView.isInitialized || !::customPlaceCard.isInitialized) {
            Toast.makeText(this, "UI components not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // 初始化 SearchResultsView
        searchResultsView.initialize(
            SearchResultsView.Configuration(
                commonConfiguration = CommonSearchViewConfiguration()
            )
        )

        // 设置 Go here 按钮点击事件
        goHereButton.setOnClickListener {
            handleGoHereClick()
        }
    }

    private fun setupMap() {
        mapView.mapboxMap.also { mapboxMap ->
            this.mapboxMap = mapboxMap
            mapboxMap.loadStyle(Style.MAPBOX_STREETS) {
                mapView.location.updateSettings {
                    enabled = true
                }
                mapView.location.addOnIndicatorPositionChangedListener(object : OnIndicatorPositionChangedListener {
                    override fun onIndicatorPositionChanged(point: Point) {
                        mapView.mapboxMap.setCamera(
                            CameraOptions.Builder()
                                .center(point)
                                .zoom(14.0)
                                .build()
                        )
                        mapView.location.removeOnIndicatorPositionChangedListener(this)
                    }
                })
            }
        }

        mapMarkersManager = MapMarkersManager(mapView)
        mapboxMap.addOnMapLongClickListener {
            reverseGeocoding(it)
            return@addOnMapLongClickListener true
        }

        // 获取默认位置 - 简化实现，不使用 defaultLocationProvider
        // 如果需要位置功能，可以在这里添加位置获取逻辑
    }

    private fun setupPlaceAutocompleteAdapter() {
        // 创建 PlaceAutocompleteUiAdapter
        placeAutocompleteUiAdapter = PlaceAutocompleteUiAdapter(
            view = searchResultsView,
            placeAutocomplete = placeAutocomplete
        )

        // 添加搜索监听器
        placeAutocompleteUiAdapter.addSearchListener(object : PlaceAutocompleteUiAdapter.SearchListener {
            override fun onSuggestionsShown(suggestions: List<PlaceAutocompleteSuggestion>) {
                // 建议显示时的处理
            }

            override fun onSuggestionSelected(suggestion: PlaceAutocompleteSuggestion) {
                // 用户选择建议时的处理
                openPlaceCard(suggestion)
            }

            override fun onPopulateQueryClick(suggestion: PlaceAutocompleteSuggestion) {
                // 用户点击填充查询时的处理
                queryEditText.setText(suggestion.name)
            }

            override fun onError(e: Exception) {
                // 错误处理
            }
        })
    }

    private fun setupTextWatcher() {
        queryEditText.addTextChangedListener(object : TextWatcher {
            override fun onTextChanged(text: CharSequence, start: Int, before: Int, count: Int) {
                if (ignoreNextQueryUpdate) {
                    ignoreNextQueryUpdate = false
                } else {
                    closePlaceCard()
                }

                lifecycleScope.launchWhenStarted {
                    placeAutocompleteUiAdapter.search(text.toString())
                    searchResultsView.isVisible = text.isNotEmpty()
                }
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                // 不需要实现
            }

            override fun afterTextChanged(s: Editable) {
                // 不需要实现
            }
        })
    }

    private fun reverseGeocoding(point: Point) {
        val types: List<PlaceAutocompleteType> = when (mapboxMap.cameraState.zoom) {
            in 0.0..4.0 -> REGION_LEVEL_TYPES
            in 4.0..6.0 -> DISTRICT_LEVEL_TYPES
            in 6.0..12.0 -> LOCALITY_LEVEL_TYPES
            else -> ALL_TYPES
        }

        lifecycleScope.launchWhenStarted {
            val response = placeAutocomplete.reverse(point, PlaceAutocompleteOptions(types = types))
            response.onValue { suggestions ->
                if (suggestions.isEmpty()) {
                    showToast("No place found at this location")
                } else {
                    openPlaceCard(suggestions.first())
                }
            }.onError { error ->
                Log.d(LOG_TAG, "Reverse geocoding error", error)
                showToast("Reverse geocoding failed")
            }
        }
    }

    private fun openPlaceCard(suggestion: PlaceAutocompleteSuggestion) {
        ignoreNextQueryUpdate = true
        queryEditText.setText("")

        lifecycleScope.launchWhenStarted {
            placeAutocomplete.select(suggestion).onValue { result ->
                mapMarkersManager.showMarker(result.coordinate)

                // 使用自定义地点卡片，传递 suggestion 以获取距离信息
                showCustomPlaceCard(result, suggestion)

                queryEditText.hideKeyboard()
                searchResultsView.isVisible = false

                // 如果这是从插件调用的，返回结果
                if (intent.getBooleanExtra("return_result", false)) {
                    returnAutocompleteResult(result)
                }
            }.onError { error ->
                Log.d(LOG_TAG, "Suggestion selection error", error)
                showToast("Selection failed")
            }
        }
    }

    private fun showCustomPlaceCard(result: com.mapbox.search.autocomplete.PlaceAutocompleteResult, suggestion: PlaceAutocompleteSuggestion? = null) {
        // 保存当前选中的地点
        currentSelectedPlace = SearchPlace.createFromPlaceAutocompleteResult(result)

        // 设置地点信息
        placeNameText.text = result.name

        // 构建详细地址
        val addressParts = mutableListOf<String>()

        // 添加地址组件
        result.address?.let { address ->
            address.houseNumber?.let { addressParts.add(it) }
            address.street?.let { addressParts.add(it) }
            address.neighborhood?.let { addressParts.add(it) }
            address.locality?.let { addressParts.add(it) }
            address.place?.let { addressParts.add(it) }
            address.district?.let { addressParts.add(it) }
            address.region?.let { addressParts.add(it) }
            address.country?.let { addressParts.add(it) }
        }

        // 如果没有详细地址，使用基本信息
        if (addressParts.isEmpty()) {
            result.categories?.firstOrNull()?.let { addressParts.add(it) }
        }

        placeAddressText.text = if (addressParts.isNotEmpty()) {
            addressParts.joinToString(", ")
        } else {
            "Address not available"
        }

        // 使用 suggestion 中的距离信息
        suggestion?.distanceMeters?.let { distanceMeters ->
            val distanceKm = distanceMeters / 1000.0
            distanceText.text = when {
                distanceMeters < 1000 -> "${distanceMeters.toInt()} m"
                distanceKm < 10.0 -> String.format("%.1f km", distanceKm)
                else -> String.format("%.0f km", distanceKm)
            }
        } ?: run {
            // 如果没有距离信息，显示默认文本
            distanceText.text = "-- km"
        }

        // 显示卡片
        customPlaceCard.visibility = View.VISIBLE
    }



    private fun closePlaceCard() {
        customPlaceCard.visibility = View.GONE
        mapMarkersManager.clearMarkers()
        currentSelectedPlace = null
    }

    private fun returnAutocompleteResult(result: com.mapbox.search.autocomplete.PlaceAutocompleteResult) {
        val resultIntent = Intent().apply {
            putExtra(RESULT_EXTRA_NAME, result.name)
            putExtra(RESULT_EXTRA_ADDRESS, result.address?.toString() ?: "")
            result.coordinate?.let { coordinate ->
                putExtra(RESULT_EXTRA_LATITUDE, coordinate.latitude())
                putExtra(RESULT_EXTRA_LONGITUDE, coordinate.longitude())
            }
        }
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }

    private fun showToast(message: String) = Toast.makeText(this, message, Toast.LENGTH_LONG).show()

    private fun isPermissionGranted(permission: String): Boolean =
        ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED

    private fun geoIntent(point: Point): Intent =
        Intent(Intent.ACTION_VIEW, Uri.parse("geo:0,0?q=${point.latitude()}, ${point.longitude()}"))

    private fun shareIntent(searchPlace: SearchPlace): Intent {
        val text = "${searchPlace.name}. " +
                "Address: ${searchPlace.address?.formattedAddress(SearchAddress.FormatStyle.Short) ?: "unknown"}. " +
                "Geo coordinate: (lat=${searchPlace.coordinate.latitude()}, lon=${searchPlace.coordinate.longitude()})"
        return Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, text)
        }
    }

    private fun View.hideKeyboard() =
        (getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager).hideSoftInputFromWindow(windowToken, 0)

    private fun handleGoHereClick() {
        // 获取当前选中的地点信息
        val placeName = placeNameText.text.toString()
        val placeAddress = placeAddressText.text.toString()

        // 从当前选中的地点获取坐标
        currentSelectedPlace?.let { place ->
            // 构建导航参数
            val navigationData = mapOf(
                "name" to placeName,
                "address" to placeAddress,
                "latitude" to place.coordinate.latitude(),
                "longitude" to place.coordinate.longitude(),
                "action" to "navigate" // 标识这是导航操作
            )

            val resultIntent = Intent().apply {
                putExtra(RESULT_EXTRA_NAME, placeName)
                putExtra(RESULT_EXTRA_PLACE_NAME, placeName)
                putExtra(RESULT_EXTRA_ADDRESS, placeAddress)
                putExtra(RESULT_EXTRA_LATITUDE, place.coordinate.latitude())
                putExtra(RESULT_EXTRA_LONGITUDE, place.coordinate.longitude())
                putExtra("action", "navigate") // 添加导航标识
            }
            setResult(Activity.RESULT_OK, resultIntent)
            finish()
        }
    }

    private fun getMapboxAccessToken(): String? {
        return try {
            var token: String? = null

            // 1. 从应用元数据获取
            val ai = packageManager.getApplicationInfo(packageName, android.content.pm.PackageManager.GET_META_DATA)
            ai.metaData?.let { metaData ->
                token = metaData.getString("com.mapbox.token")
                    ?: metaData.getString("MAPBOX_ACCESS_TOKEN")
            }

            // 2. 从字符串资源获取
            if (token == null) {
                try {
                    val resId = resources.getIdentifier("mapbox_access_token", "string", packageName)
                    if (resId != 0) {
                        token = getString(resId)
                    }
                } catch (e: Exception) {
                    // 忽略错误
                }
            }

            token
        } catch (e: Exception) {
            null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理资源 - lateinit 变量不需要手动清理
    }

    // 地图标记管理器
    private class MapMarkersManager(mapView: MapView) {
        private val mapboxMap = mapView.mapboxMap
        private val circleAnnotationManager = mapView.annotations.createCircleAnnotationManager(null)
        private val markers = mutableMapOf<String, Point>()

        fun clearMarkers() {
            markers.clear()
            circleAnnotationManager.deleteAll()
        }

        fun showMarker(coordinate: Point) {
            clearMarkers()
            val circleAnnotationOptions: CircleAnnotationOptions = CircleAnnotationOptions()
                .withPoint(coordinate)
                .withCircleRadius(8.0)
                .withCircleColor("#ee4e8b")
                .withCircleStrokeWidth(2.0)
                .withCircleStrokeColor("#ffffff")

            val annotation = circleAnnotationManager.create(circleAnnotationOptions)
            markers[annotation.id] = coordinate

            CameraOptions.Builder()
                .center(coordinate)
                .padding(MARKERS_INSETS_OPEN_CARD)
                .zoom(14.0)
                .build().also {
                    mapboxMap.setCamera(it)
                }
        }
    }
}
