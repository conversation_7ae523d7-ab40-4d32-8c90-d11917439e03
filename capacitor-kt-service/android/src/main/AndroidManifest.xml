<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    
    <!-- 位置权限（Mapbox导航需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    
    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.PARTIAL_WAKE_LOCK" />
    
    <!-- 电池优化豁免权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    
    <!-- 硬件特性 -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="false" />
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />
    <uses-feature android:name="android.hardware.location" android:required="false" />
    <uses-feature android:name="android.hardware.location.gps" android:required="false" />

    <application>
        <!-- 蓝牙前台服务声明 -->
        <service
            android:name="com.kunteng.plugins.kt.BluetoothForegroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />
        
        <!-- Mapbox导航Activity -->
        <activity
            android:name="com.kunteng.plugins.kt.NavigationActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false"
            tools:ignore="MissingClass" />
            
        <!-- Mapbox导航对话框Fragment -->
        <activity
            android:name="com.kunteng.plugins.kt.NavigationDialogFragment"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false"
            tools:ignore="Instantiatable,MissingClass">
            <intent-filter>
                <action android:name="com.kunteng.plugins.kt.NavigationViewActivity"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        
        <!-- Mapbox搜索框Activity -->
        <activity
            android:name="com.kunteng.plugins.kt.MapboxSearchBoxActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="MissingClass" />
            
        <!-- Mapbox地点自动完成Activity -->
        <activity
            android:name="com.kunteng.plugins.kt.MapboxPlaceAutocompleteActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="MissingClass" />
    </application>

</manifest>
