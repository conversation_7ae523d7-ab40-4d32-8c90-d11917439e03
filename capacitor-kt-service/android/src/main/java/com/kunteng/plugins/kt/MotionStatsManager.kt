package com.kunteng.plugins.kt

import kotlin.math.max

/**
 * 运动统计管理器（MotionStatsManager）
 *
 * 统一管理与“运动”相关的实时统计：
 * - 速度：当前/平均/最大（单位：m/s，外部可自行格式化为 km/h 或 mph）
 * - 海拔：当前/最低/最高/累计爬升/累计下降（单位：米）
 *
 * 说明：
 * - 负速度会被裁剪为 0，避免异常数据影响统计。
 * - 低于阈值 [minSpeedThresholdMs] 的速度被判定为静止，不累加“移动距离”。
 */
class MotionStatsManager(
    // 最小速度阈值（m/s）。低于此阈值判定为静止，用于避免漂移导致的虚假移动距离
    private val minSpeedThresholdMs: Double = 0.5
) {
    interface Listener {
        /**
         * @param currentSpeedMs 当前速度（m/s）
         * @param averageSpeedMs 平均速度（m/s）
         * @param maxSpeedMs 最大速度（m/s）
         * @param currentAltitudeM 当前海拔（米）
         * @param minAltitudeM 最低海拔（米）
         * @param maxAltitudeM 最高海拔（米）
         * @param totalAscentM 累计爬升（米）
         * @param totalDescentM 累计下降（米）
         */
        fun onStatsUpdate(
            currentSpeedMs: Double,
            averageSpeedMs: Double,
            maxSpeedMs: Double,
            currentAltitudeM: Double,
            minAltitudeM: Double,
            maxAltitudeM: Double,
            totalAscentM: Double,
            totalDescentM: Double,
        )
    }

    private var listener: Listener? = null

    // 速度统计
    private var currentSpeedMs: Double = 0.0
    private var maxSpeedMs: Double = 0.0
    private var totalDistanceMeters: Double = 0.0
    private var totalElapsedTimeMs: Long = 0
    private var lastUpdateTimestampMs: Long? = null

    // 海拔统计
    private var currentAltitudeMeters: Double = 0.0
    private var minAltitudeMeters: Double? = null
    private var maxAltitudeMeters: Double? = null
    private var lastAltitudeMeters: Double? = null
    private var totalAscentMeters: Double = 0.0
    private var totalDescentMeters: Double = 0.0

    fun setListener(listener: Listener?) {
        this.listener = listener
    }

    /** 重置所有统计数据，并触发一次 0 值回调以便 UI 立即清零 */
    fun reset() {
        // 速度
        currentSpeedMs = 0.0
        maxSpeedMs = 0.0
        totalDistanceMeters = 0.0
        totalElapsedTimeMs = 0
        lastUpdateTimestampMs = null
        // 海拔
        currentAltitudeMeters = 0.0
        minAltitudeMeters = null
        maxAltitudeMeters = null
        lastAltitudeMeters = null
        totalAscentMeters = 0.0
        totalDescentMeters = 0.0

        listener?.onStatsUpdate(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    }

    /**
     * 统一更新速度与海拔统计。
     * @param speedMetersPerSecond 速度（m/s），负值将被裁剪为 0
     * @param altitudeMeters 海拔（米），可空；传 null 则不更新海拔统计
     * @param timestampMs 本次样本时间（毫秒），默认当前时间
     */
    fun updateStats(
        speedMetersPerSecond: Double,
        altitudeMeters: Double?,
        timestampMs: Long = System.currentTimeMillis()
    ) {
        // 1) 速度数据清洗
        val validSpeed = max(0.0, speedMetersPerSecond)

        // 2) 时间累计与移动距离累计
        val previousTimestamp = lastUpdateTimestampMs
        if (previousTimestamp != null) {
            val deltaMs = (timestampMs - previousTimestamp).coerceAtLeast(0)
            totalElapsedTimeMs += deltaMs
            if (validSpeed >= minSpeedThresholdMs && deltaMs > 0) {
                totalDistanceMeters += validSpeed * (deltaMs / 1000.0)
            }
        }

        // 3) 更新当前/最大速度与时间戳
        lastUpdateTimestampMs = timestampMs
        currentSpeedMs = validSpeed
        if (validSpeed > maxSpeedMs) {
            maxSpeedMs = validSpeed
        }

        // 4) 海拔统计
        if (altitudeMeters != null) {
            currentAltitudeMeters = altitudeMeters
            minAltitudeMeters = if (minAltitudeMeters == null) altitudeMeters else kotlin.math.min(minAltitudeMeters!!, altitudeMeters)
            maxAltitudeMeters = if (maxAltitudeMeters == null) altitudeMeters else kotlin.math.max(maxAltitudeMeters!!, altitudeMeters)
            val prevAlt = lastAltitudeMeters
            if (prevAlt != null) {
                val delta = altitudeMeters - prevAlt
                if (delta > 0) totalAscentMeters += delta else totalDescentMeters += -delta
            }
            lastAltitudeMeters = altitudeMeters
        }

        // 5) 平均速度 = 距离 / 时间（m/s）
        val avgMs = if (totalElapsedTimeMs > 0) totalDistanceMeters / (totalElapsedTimeMs / 1000.0) else 0.0

        // 6) 通知监听器
        listener?.onStatsUpdate(
            currentSpeedMs,
            avgMs,
            maxSpeedMs,
            currentAltitudeMeters,
            (minAltitudeMeters ?: currentAltitudeMeters),
            (maxAltitudeMeters ?: currentAltitudeMeters),
            totalAscentMeters,
            totalDescentMeters,
        )
    }
}


