import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from 'vitest'
import { useBluetoothData } from '../../src/composables/useBluetoothData'
import { useSettingStore } from '../../src/store/useSettingStore'
import { useDashboardStore } from '../../src/store/useDashboardStore'
import { WriteData } from '../../src/const/ble.const'
import { GearDirection, LightDirection } from '../../src/const/bike.const'

// Mock Vue composition API
vi.mock('vue', () => ({
  ref: vi.fn((value) => ({ value })),
  computed: vi.fn((getter) => ({ value: getter() })),
  watch: vi.fn(),
  readonly: vi.fn((value) => value)
}))

// Mock Pinia
vi.mock('pinia', () => ({
  storeToRefs: vi.fn((store) => {
    const refs = {}
    Object.keys(store).forEach(key => {
      if (typeof store[key] !== 'function') {
        refs[key] = { value: store[key] }
      }
    })
    return refs
  })
}))

// Mock stores - 与 useSetting 测试用例完全相同的数据
const mockSettingStore = {
  dimension: 5,
  maxSpeed: 72,
  p1: 88,
  p2: 1,
  p3: 1,
  p4: 0,
  p5: 15,
  c1: 7,
  c2: 0,
  c3: 8,
  c4: 4,
  c5: 10,
  c7: 0,
  c12: 4,
  c13: 1,
  c14: 2,
  percent: 50,
  handlebarMaxSpeed: 20
}

const mockDashboardStore = {
  gearPosition: 0,
  lightStatus: false
}

vi.mock('../../src/store/useSettingStore', () => ({
  useSettingStore: vi.fn(() => mockSettingStore)
}))

vi.mock('../../src/store/useDashboardStore', () => ({
  useDashboardStore: vi.fn(() => mockDashboardStore)
}))

describe('useBluetoothData', () => {
  let bluetoothData: ReturnType<typeof useBluetoothData>

  // 创建初始WriteData的辅助函数
  const createInitialWriteData = () => [...WriteData]

  beforeAll(() => {
    console.log('开始运行 useBluetoothData 测试套件')
  })

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()

    // 重置store状态
    Object.assign(mockSettingStore, {
      dimension: 5,
      maxSpeed: 72,
      p1: 88,
      p2: 1,
      p3: 1,
      p4: 0,
      p5: 15,
      c1: 7,
      c2: 0,
      c3: 8,
      c4: 4,
      c5: 10,
      c7: 0,
      c12: 4,
      c13: 1,
      c14: 2,
      percent: 50,
      handlebarMaxSpeed: 20
    })

    Object.assign(mockDashboardStore, {
      gearPosition: 0,
      lightStatus: false
    })

    // 创建 useBluetoothData 实例
    bluetoothData = useBluetoothData()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化writeData', () => {
      expect(bluetoothData.writeData.value).toBeDefined()
      expect(bluetoothData.writeData.value.length).toBe(18)
    })

    it('应该返回正确的方法', () => {
      expect(bluetoothData.Int2Bytes).toBeDefined()
      expect(bluetoothData.calculateChecksum).toBeDefined()
      expect(bluetoothData.updateNavigationData).toBeDefined()
      expect(bluetoothData.currentSendData).toBeDefined()
      expect(typeof bluetoothData.Int2Bytes).toBe('function')
      expect(typeof bluetoothData.calculateChecksum).toBe('function')
      expect(typeof bluetoothData.updateNavigationData).toBe('function')
    })
  })

  describe('Int2Bytes 工具函数', () => {
    it('应该正确转换数字到字节', () => {
      // 测试P5参数映射到索引0
      mockSettingStore.p5 = 15
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[0]).toBe(15)
    })

    it('应该正确处理字符串输入', () => {
      mockSettingStore.p5 = '25' as any
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[0]).toBe(25)
    })

    it('应该正确处理边界值', () => {
      mockSettingStore.p5 = 255
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[0]).toBe(255)
    })

    it('应该正确处理超出范围的值', () => {
      mockSettingStore.p5 = 300
      bluetoothData = useBluetoothData()
      
      // 300 & 0xFF = 44
      expect(bluetoothData.writeData.value[0]).toBe(44)
    })

    it('应该正确处理无效值', () => {
      mockSettingStore.p5 = NaN
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[0]).toBe(0)
    })
  })

  describe('轮径扩展逻辑', () => {
    it('应该正确处理小于10的轮径值', () => {
      mockSettingStore.dimension = 5
      bluetoothData = useBluetoothData()
      
      // 验证轮径扩展位为0（小于10不需要扩展）
      const byte4 = bluetoothData.writeData.value[4]
      const expand = byte4 & 0x80
      expect(expand).toBe(0)
    })

    it('应该正确处理大于等于10的轮径值', () => {
      mockSettingStore.dimension = 15
      bluetoothData = useBluetoothData()
      
      // 验证轮径扩展位为0x80（大于等于10需要扩展）
      const byte4 = bluetoothData.writeData.value[4]
      const expand = byte4 & 0x80
      expect(expand).toBe(0x80)
    })
  })

  describe('速度计算', () => {
    it('应该正确设置小于42的最大速度', () => {
      mockSettingStore.maxSpeed = 30
      mockSettingStore.dimension = 5
      mockSettingStore.p2 = 1
      mockSettingStore.p3 = 1
      mockSettingStore.p4 = 0
      bluetoothData = useBluetoothData()
      
      // speed = 30 - 10 = 20, speedBit = 20 << 3 = 160
      // secondData = 160 + 5 = 165
      expect(bluetoothData.writeData.value[2]).toBe(165)
    })

    it('应该正确处理速度为42的边界情况', () => {
      mockSettingStore.maxSpeed = 42 // speed = 32
      mockSettingStore.dimension = 5
      bluetoothData = useBluetoothData()
      
      // speedDiff = 0 的特殊情况
      expect(bluetoothData.writeData.value[2]).toBe(0x5)
    })

    it('应该正确设置大于42的最大速度', () => {
      mockSettingStore.maxSpeed = 50
      mockSettingStore.dimension = 5
      bluetoothData = useBluetoothData()
      
      // speed = 40, speedDiff = 8, speedBit = 64
      // secondData = 64 + 5 = 69
      expect(bluetoothData.writeData.value[2]).toBe(69)
    })
  })

  describe('参数映射验证', () => {
    it('P1参数应该映射到索引3', () => {
      mockSettingStore.p1 = 88
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[3]).toBe(88)
    })

    it('C1C2参数应该正确映射到索引6', () => {
      mockSettingStore.c1 = 7
      mockSettingStore.c2 = 0
      bluetoothData = useBluetoothData()
      
      // (7 << 3) + 0 = 56
      expect(bluetoothData.writeData.value[6]).toBe(56)
    })

    it('C5C14参数应该正确映射到索引7', () => {
      mockSettingStore.c5 = 10
      mockSettingStore.c14 = 2
      bluetoothData = useBluetoothData()
      
      // 128 + (2 << 5) + 10 = 128 + 64 + 10 = 202
      expect(bluetoothData.writeData.value[7]).toBe(202)
    })

    it('C4C7C12参数应该正确映射到索引8', () => {
      mockSettingStore.c4 = 4
      mockSettingStore.c7 = 0
      mockSettingStore.c12 = 4
      bluetoothData = useBluetoothData()
      
      // (4 << 5) + (0 << 3) + 4 = 128 + 0 + 4 = 132
      expect(bluetoothData.writeData.value[8]).toBe(132)
    })

    it('把手最大速度应该映射到索引9', () => {
      mockSettingStore.handlebarMaxSpeed = 20
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[9]).toBe(20)
    })

    it('C13参数应该正确映射到索引10', () => {
      mockSettingStore.c13 = 1
      bluetoothData = useBluetoothData()
      
      // (1 << 2) + 1 = 4 + 1 = 5
      expect(bluetoothData.writeData.value[10]).toBe(5)
    })

    it('百分比应该映射到索引11', () => {
      mockSettingStore.percent = 50
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[11]).toBe(50)
    })
  })

  describe('档位和灯光状态', () => {
    it('应该正确处理档位0和灯光关闭', () => {
      mockDashboardStore.gearPosition = 0
      mockDashboardStore.lightStatus = false
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[1]).toBe(GearDirection.positionZero + LightDirection.off)
    })

    it('应该正确处理档位3和灯光开启', () => {
      mockDashboardStore.gearPosition = 3
      mockDashboardStore.lightStatus = true
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[1]).toBe(GearDirection.positionThree + LightDirection.on)
    })

    it('C3参数应该影响档位设置', () => {
      mockSettingStore.c3 = 2  // 设置档位为2
      mockDashboardStore.lightStatus = false
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[1]).toBe(GearDirection.positionTwo + LightDirection.off)
    })

    it('C3为8时应该不影响档位', () => {
      mockSettingStore.c3 = 8  // 特殊值，不改变档位
      mockDashboardStore.gearPosition = 0
      mockDashboardStore.lightStatus = false
      bluetoothData = useBluetoothData()
      
      expect(bluetoothData.writeData.value[1]).toBe(GearDirection.positionZero + LightDirection.off)
    })
  })

  describe('固定值验证', () => {
    it('字节17应该始终是0x0e', () => {
      bluetoothData = useBluetoothData()

      expect(bluetoothData.writeData.value[17]).toBe(0x0e)

      // 修改参数后应该保持不变
      mockSettingStore.p5 = 100
      bluetoothData = useBluetoothData()
      expect(bluetoothData.writeData.value[17]).toBe(0x0e)
    })
  })

  describe('导航数据保留', () => {
    it('应该保留导航数据不被覆盖', () => {
      // 先设置导航数据
      bluetoothData.updateNavigationData(0x81, 100, 50, 200)
      bluetoothData = useBluetoothData()
      
      // 验证导航数据被保留
      expect(bluetoothData.writeData.value[12]).toBe(0x81)
      expect(bluetoothData.writeData.value[13]).toBe(100)
      expect(bluetoothData.writeData.value[14]).toBe(50)
      expect(bluetoothData.writeData.value[15]).toBe(200)
    })
  })

  describe('校验和计算', () => {
    it('应该正确计算索引5的校验和', () => {
      bluetoothData = useBluetoothData()
      const data = bluetoothData.writeData.value
      
      // 手动计算校验和
      const expectedChecksum = data[1] ^ data[2] ^ data[3] ^ data[4] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11]
      expect(data[5]).toBe(expectedChecksum)
    })

    it('应该正确计算索引16的校验和', () => {
      // 🔧 重置导航数据确保测试一致性
      bluetoothData = useBluetoothData()
      bluetoothData.updateNavigationData(0, 0, 0, 0)

      const data = bluetoothData.writeData.value

      // 手动计算校验和
      const expectedChecksum = data[1] ^ data[2] ^ data[3] ^ data[4] ^ data[6] ^ data[7] ^ data[8] ^ data[9] ^ data[10] ^ data[11] ^ data[12] ^ data[13] ^ data[14] ^ data[15]
      expect(data[16]).toBe(expectedChecksum)
    })
  })

  describe('边界情况处理', () => {
    it('应该正确处理所有参数为0的情况', () => {
      Object.keys(mockSettingStore).forEach(key => {
        if (typeof mockSettingStore[key] === 'number') {
          mockSettingStore[key] = 0
        }
      })
      mockDashboardStore.gearPosition = 0
      mockDashboardStore.lightStatus = false
      
      bluetoothData = useBluetoothData()
      
      // 应该不抛出错误
      expect(bluetoothData.writeData.value).toBeDefined()
      expect(bluetoothData.writeData.value.length).toBe(18)
    })

    it('应该正确处理极大值', () => {
      mockSettingStore.p5 = 999999
      bluetoothData = useBluetoothData()
      
      // 应该被正确截断
      expect(bluetoothData.writeData.value[0]).toBe(999999 & 0xFF)
    })
  })
})
