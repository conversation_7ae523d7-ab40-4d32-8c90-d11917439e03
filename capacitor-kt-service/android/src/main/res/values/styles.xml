<resources>
    <style name="KtMaterialTheme.NoActionBar" parent="Theme.MaterialComponents.DayNight.NoActionBar"/>
    <style name="AppTheme.MaterialCompat" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- material defaults -->
    </style>
    <style name="PrimaryManeuverTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">30sp</item>
    </style>
    <style name="ManeuverTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">25sp</item>
    </style>
    <style name="StepDistanceRemainingAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">21sp</item>
    </style>

    <!-- MaterialComponents theme overlay for buttons if needed -->
    <style name="Widget.KT.MaterialButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textAllCaps">false</item>
    </style>
</resources>
