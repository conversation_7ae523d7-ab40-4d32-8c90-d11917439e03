<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Mapbox</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-list>
        <ion-item>
          <ion-input v-model="longitude" label="longitude"></ion-input>
        </ion-item>
        <ion-item>
          <ion-input v-model="latitude" label="latitude"></ion-input>
        </ion-item>
      </ion-list>
      <ion-button class="ion-margin-top ion-padding" expand="block"
        @click="onNavigateButtonClick">StartNavigate</ion-button>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonList,
  IonInput,
  IonButton,
  IonItem
} from '@ionic/vue'
import { onMounted, onUnmounted, ref } from 'vue'
import { useToast } from '@/hooks/useToast'
import { CapacitorKtService } from 'capacitor-kt-service'
import { Capacitor } from '@capacitor/core'
import { useNavigation } from '@/hooks/useNavigation'
import { getGlobalNavigationStateManager } from '@/hooks/useNavigationStateManager'

const isNative = Capacitor.isNativePlatform();
const { presentToast } = useToast()
// 保留 useNavigation 的导入以防其他地方需要，但这里主要使用全局状态管理器
const navigationStateManager = getGlobalNavigationStateManager()

const longitude = ref<number>(120.5481) // 经度
const latitude = ref<number>(31.3012) // 纬度

const onNavigateButtonClick = () => {
  const long = longitude.value
  const lat = latitude.value
  navigateToAddressWithMapBox({ latitude: lat, longitude: long })
}

const navigateToAddressWithMapBox = async ({
  latitude = 0,
  longitude = 0
}) => {
  if (!isAddressValid({ latitude, longitude })) {
    return
  }

  try {
    await startNavigate({ latitude, longitude })
  } catch (error) {
    handleDeniedLocation(error)
  }
}

const handleDeniedLocation = (error: any) => {
  if (error?.type === 'not_supported') {
    return presentToast('Navigation not supported on web', 'top')
  }
  presentToast(
    'Error in getting location permission, please enable your gps location',
    'top'
  )
}

// 导航进度变化事件监听器 - 通过CapacitorKtService事件系统接收
const addOnRouteProgressChangeListener = await CapacitorKtService.addListener('onRouteProgressChange', (data: any) => {
  console.log('导航进度更新:', data)
  // 数据格式: { bannerInstructions, distanceRemaining, stepDistanceRemaining, timestamp }
  // 🔧 使用全局导航状态管理器更新进度
  navigationStateManager.updateNavigationProgress(data)
})

// 导航完成事件监听器
const addOnNavigationCompleteListener = await CapacitorKtService.addListener('onNavigationComplete', () => {
  console.log('导航已完成')
  presentToast('导航已完成', 'top')

  // 🔧 使用全局导航状态管理器完成导航
  navigationStateManager.completeNavigation()
})

// 屏幕镜像变化事件监听器
const addOnScreenMirroringChangeListener = await CapacitorKtService.addListener('onScreenMirroringChange', (data: any) => {
  console.log('镜像状态变化:', data)
  const status = data.enabled ? '开启' : '关闭'
  console.log(`${status}导航镜像 - 蓝牙数据发送由Native端自动处理`)
  presentToast(`导航镜像已${status}`, 'top')
})

// 加号按钮点击事件监听器
const addOnPlusButtonClickListener = await CapacitorKtService.addListener('plusButtonClicked', (data: any) => {
  console.log('plusButtonClicked', data)
})

// 减号按钮点击事件监听器
const addOnMinusButtonClickListener = await CapacitorKtService.addListener('minusButtonClicked', async (data: any) => {
  console.log('minusButtonClicked', data)
})

// 导航停止事件监听器
const addOnNavigationStopListener = await CapacitorKtService.addListener('onNavigationStop', (data: any) => {
  console.log('导航已停止:', data)
  presentToast('导航已停止', 'top')

  // 🔧 使用全局导航状态管理器停止导航
  navigationStateManager.stopNavigation('user_stopped')
})

const addMapboxEventListener = async () => {
  await addOnRouteProgressChangeListener
  await addOnScreenMirroringChangeListener
  await addOnPlusButtonClickListener
  await addOnMinusButtonClickListener
  await addOnNavigationStopListener
  await addOnNavigationCompleteListener
  console.log('Mapbox event listeners initialized')
}

const removeMapboxEventListener = async () => {
  await addOnRouteProgressChangeListener.remove()
  await addOnScreenMirroringChangeListener.remove()
  await addOnPlusButtonClickListener.remove()
  await addOnMinusButtonClickListener.remove()
  await addOnNavigationStopListener.remove()
  await addOnNavigationCompleteListener.remove()
  console.log('Mapbox event listeners removed')
}

onMounted(async () => {
  await addMapboxEventListener()
})

onUnmounted(async () => {
  await removeMapboxEventListener()

  // 🔧 页面卸载时使用全局导航状态管理器清理状态
  console.log('🧹 页面卸载，清理导航数据状态')
  navigationStateManager.stopNavigation('page_unmounted')
})

const startNavigate = async ({ latitude = 0, longitude = 0 }) => {
  try {
    // 使用CapacitorKtService的showMapboxNavigation方法
    const result = await CapacitorKtService.showMapboxNavigation({
      routes: [
        {
          latitude: latitude,
          longitude: longitude
        }
      ],
      simulating: false
    })
    
    // 导航启动成功
    console.log('导航启动成功:', result)
    
    // 根据重构后的逻辑，成功启动会返回成功状态
    if (result.status === 'success') {
      presentToast(result.message || '导航启动成功', 'top')

      // 🔧 通知全局导航状态管理器导航已启动
      navigationStateManager.startNavigation({
        latitude: latitude,
        longitude: longitude,
        startTime: Date.now()
      })
    } else {
      // 处理启动失败的情况
      throw new Error(result.message || '导航启动失败')
    }

    // 导航数据现在通过事件监听器自动接收，无需额外处理
    
  } catch (error: any) {
    // 导航启动失败
    console.error('导航启动失败:', error)
    
    // 处理具体的错误类型
    let errorMessage = '导航启动失败'
    if (error.message) {
      if (error.message.includes('Route Navigation cancelled') || error.message.includes('NAVIGATION_START_FAILED')) {
        errorMessage = '路线计算已取消'
      } else if (error.message.includes('Failed to calculate route')) {
        errorMessage = '路线计算失败'
      } else if (error.message.includes('Invalid destination coordinates')) {
        errorMessage = '目标坐标无效'
      } else {
        errorMessage = error.message
      }
    }
    
    presentToast(errorMessage, 'top')
  }
}

const isAddressValid = ({ latitude = 0, longitude = 0 }) => {
  if (latitude === 0 || longitude === 0) {
    presentToast('Activity Address is not available', 'top')
    return false
  }

  return true
}
</script>

<style scoped lang="scss"></style>
