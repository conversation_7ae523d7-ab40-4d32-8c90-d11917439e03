package com.kunteng.plugins.kt

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.mapbox.search.autocomplete.PlaceAutocomplete
import com.mapbox.search.autocomplete.PlaceAutocompleteSuggestion
import com.mapbox.search.ui.adapter.autocomplete.PlaceAutocompleteUiAdapter
import com.mapbox.search.ui.view.CommonSearchViewConfiguration
import com.mapbox.search.ui.view.SearchResultsView
import com.mapbox.common.MapboxOptions
import kotlinx.coroutines.launch

class MapboxSearchBoxActivity : AppCompatActivity() {

    companion object {
        const val RESULT_EXTRA_NAME = "result_name"
        const val RESULT_EXTRA_PLACE_NAME = "result_name" // 为了兼容性
        const val RESULT_EXTRA_ADDRESS = "result_address"
        const val RESULT_EXTRA_LATITUDE = "result_latitude"
        const val RESULT_EXTRA_LONGITUDE = "result_longitude"
    }

    private lateinit var placeAutocomplete: PlaceAutocomplete
    private lateinit var searchResultsView: SearchResultsView
    private lateinit var placeAutocompleteUiAdapter: PlaceAutocompleteUiAdapter
    private lateinit var queryEditText: EditText
    private var ignoreNextQueryUpdate = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val layoutId = getSearchBoxLayoutId()
        if (layoutId == 0) {
            Toast.makeText(this, "Search box layout not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        setContentView(layoutId)

        // 设置 Mapbox Access Token
        val accessToken = getMapboxAccessToken()
        if (accessToken.isNullOrEmpty()) {
            Toast.makeText(this, "Mapbox access token not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        MapboxOptions.accessToken = accessToken

        // 创建 PlaceAutocomplete 实例
        placeAutocomplete = PlaceAutocomplete.create()

        initializeViews()
        setupPlaceAutocompleteAdapter()
        setupTextWatcher()
    }

    private fun getSearchBoxLayoutId(): Int {
        // 动态获取布局资源ID
        return resources.getIdentifier("activity_mapbox_search_box", "layout", packageName)
    }

    private fun initializeViews() {
        queryEditText = findViewById(resources.getIdentifier("query_edit_text", "id", packageName))
        searchResultsView = findViewById(resources.getIdentifier("search_results_view", "id", packageName))

        if (!::queryEditText.isInitialized || !::searchResultsView.isInitialized) {
            Toast.makeText(this, "Search UI components not found", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // 初始化 SearchResultsView
        searchResultsView.initialize(
            SearchResultsView.Configuration(
                commonConfiguration = CommonSearchViewConfiguration()
            )
        )
    }

    private fun setupPlaceAutocompleteAdapter() {
        // 创建 PlaceAutocompleteUiAdapter
        placeAutocompleteUiAdapter = PlaceAutocompleteUiAdapter(
            view = searchResultsView,
            placeAutocomplete = placeAutocomplete
        )

        // 添加搜索监听器
        placeAutocompleteUiAdapter.addSearchListener(object : PlaceAutocompleteUiAdapter.SearchListener {
            override fun onSuggestionsShown(suggestions: List<PlaceAutocompleteSuggestion>) {
                // 建议显示时的处理
            }

            override fun onSuggestionSelected(suggestion: PlaceAutocompleteSuggestion) {
                // 用户选择建议时的处理
                selectSuggestion(suggestion)
            }

            override fun onPopulateQueryClick(suggestion: PlaceAutocompleteSuggestion) {
                // 用户点击填充查询时的处理
                queryEditText.setText(suggestion.name)
            }

            override fun onError(e: Exception) {
                Toast.makeText(this@MapboxSearchBoxActivity, "Search error: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    private fun setupTextWatcher() {
        queryEditText.addTextChangedListener(object : TextWatcher {
            override fun onTextChanged(text: CharSequence, start: Int, before: Int, count: Int) {
                if (ignoreNextQueryUpdate) {
                    ignoreNextQueryUpdate = false
                } else {
                    // 清除之前的选择状态
                }

                lifecycleScope.launchWhenStarted {
                    placeAutocompleteUiAdapter.search(text.toString())
                    searchResultsView.isVisible = text.isNotEmpty()
                }
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                // 不需要实现
            }

            override fun afterTextChanged(s: Editable) {
                // 不需要实现
            }
        })
    }

    private fun selectSuggestion(suggestion: PlaceAutocompleteSuggestion) {
        ignoreNextQueryUpdate = true
        queryEditText.setText("")

        lifecycleScope.launchWhenStarted {
            placeAutocomplete.select(suggestion).onValue { result ->
                // 返回结果给调用方
                returnPlaceAutocompleteResult(result)
                searchResultsView.isVisible = false
            }.onError { error ->
                Toast.makeText(this@MapboxSearchBoxActivity, "Selection error: ${error.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    

    
    private fun returnPlaceAutocompleteResult(result: com.mapbox.search.autocomplete.PlaceAutocompleteResult) {
        val resultIntent = Intent().apply {
            putExtra(RESULT_EXTRA_NAME, result.name)
            putExtra(RESULT_EXTRA_ADDRESS, result.address?.toString() ?: "")
            result.coordinate?.let { coordinate ->
                putExtra(RESULT_EXTRA_LATITUDE, coordinate.latitude())
                putExtra(RESULT_EXTRA_LONGITUDE, coordinate.longitude())
            }
        }
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }


    
    private fun getMapboxAccessToken(): String? {
        return try {
            var token: String? = null
            
            // 1. 从应用元数据获取
            val ai = packageManager.getApplicationInfo(packageName, android.content.pm.PackageManager.GET_META_DATA)
            ai.metaData?.let { metaData ->
                token = metaData.getString("com.mapbox.token")
                    ?: metaData.getString("MAPBOX_ACCESS_TOKEN")
            }
            
            // 2. 从字符串资源获取
            if (token == null) {
                try {
                    val resId = resources.getIdentifier("mapbox_access_token", "string", packageName)
                    if (resId != 0) {
                        token = getString(resId)
                    }
                } catch (e: Exception) {
                    // 忽略错误
                }
            }
            
            token
        } catch (e: Exception) {
            null
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源 - lateinit 变量不需要手动清理
    }
}
