/**
 * 全局蓝牙服务
 * Vue 3 最佳实践：单一初始化点，通过store管理状态
 */

import { isPlatform } from '@ionic/vue'
import { useBluetoothStore } from '@/store/useBluetoothStore'
import { useBleStore } from '@/store/useBleStore'
import { storeToRefs } from 'pinia'
import { watch } from 'vue'

interface BluetoothService {
  initialize: () => Promise<void>
  startSending: () => Promise<boolean>
  stopSending: () => Promise<boolean>
  updateAndSend: () => Promise<boolean>
  destroy: () => void
}

class GlobalBluetoothService implements BluetoothService {
  private isInitialized = false
  private platformHook: any = null
  private bluetoothStore = useBluetoothStore()
  private bleStore = useBleStore()
  private unwatchConnectedDevice: (() => void) | null = null

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 蓝牙服务已初始化，跳过重复初始化')
      return
    }

    console.log('🚀 初始化全局蓝牙服务...')

    try {
      // 1. 根据平台初始化对应的hook
      await this.initializePlatformHook()

      // 2. 设置连接状态监听
      this.setupConnectionWatcher()

      // 3. 初始化蓝牙LE
      await this.initializeBluetoothLE()

      this.isInitialized = true
      console.log('✅ 全局蓝牙服务初始化完成')
    } catch (error) {
      console.error('❌ 全局蓝牙服务初始化失败:', error)
      throw error
    }
  }

  private async initializePlatformHook(): Promise<void> {
    const isAndroid = isPlatform('android')

    if (isAndroid) {
      console.log('🎯 Android平台，使用 native 方案')
      // 动态导入避免循环依赖
      const { useNativeBluetoothMessage } = await import('@/hooks/useNativeBluetoothMessage')
      
      // 移除单例包装，直接调用原始函数
      this.platformHook = this.createNativeBluetoothHook()
    } else {
      console.log('🎯 iOS/Web平台，使用 webview 方案')
      const { useMessage } = await import('@/hooks/useMessage')
      this.platformHook = useMessage()
    }
  }

  private createNativeBluetoothHook() {
    // 直接创建原生蓝牙hook的逻辑，避免单例包装
    console.log("🔧 原生蓝牙服务 - 首次初始化")
    
    // 这里应该包含useNativeBluetoothMessage的核心逻辑
    // 暂时返回一个基本实现，后续需要将useNativeBluetoothMessage的逻辑迁移到这里
    return {
      startNativeBluetoothSending: async () => {
        this.bluetoothStore.setServiceRunning(true)
        return true
      },
      stopNativeBluetoothSending: async () => {
        this.bluetoothStore.setServiceRunning(false)
        return true
      },
      updateNativeBluetoothData: async () => {
        this.bluetoothStore.updateSendData()
        return true
      }
    }
  }

  private setupConnectionWatcher(): void {
    const { connectedDevice } = storeToRefs(this.bleStore)

    this.unwatchConnectedDevice = watch(
      () => connectedDevice.value?.isPaired,
      async (isConnected) => {
        if (isConnected) {
          console.log('🎉 蓝牙连接成功，启动发送服务')
          await this.startSending()
        } else {
          console.log('📱 蓝牙断开，停止发送服务')
          await this.stopSending()
        }
      }
    )
  }

  private async initializeBluetoothLE(): Promise<void> {
    try {
      // 动态导入蓝牙LE初始化
      const { useBluetoothLe } = await import('@/hooks/useBluetooth-le')
      const { initialBle } = useBluetoothLe()
      await initialBle()
    } catch (error) {
      console.warn('⚠️ 蓝牙LE初始化失败:', error)
      // 不抛出错误，允许服务继续运行
    }
  }

  async startSending(): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('⚠️ 蓝牙服务未初始化')
      return false
    }

    try {
      console.log('🚀 启动蓝牙发送服务')
      
      if (this.platformHook?.startNativeBluetoothSending) {
        await this.platformHook.startNativeBluetoothSending()
      } else if (this.platformHook?.sendMessage) {
        await this.platformHook.sendMessage()
      }

      return true
    } catch (error) {
      console.error('❌ 启动发送服务失败:', error)
      return false
    }
  }

  async stopSending(): Promise<boolean> {
    if (!this.isInitialized) {
      return true
    }

    try {
      console.log('⏹️ 停止蓝牙发送服务')
      
      if (this.platformHook?.stopNativeBluetoothSending) {
        await this.platformHook.stopNativeBluetoothSending()
      } else if (this.platformHook?.stopSendMessage) {
        await this.platformHook.stopSendMessage()
      }

      return true
    } catch (error) {
      console.error('❌ 停止发送服务失败:', error)
      return false
    }
  }

  async updateAndSend(): Promise<boolean> {
    if (!this.isInitialized) {
      return false
    }

    try {
      console.log('🔄 更新并发送蓝牙数据')
      
      if (this.platformHook?.updateNativeBluetoothData) {
        await this.platformHook.updateNativeBluetoothData()
      } else if (this.platformHook?.sendSingleMessage) {
        await this.platformHook.sendSingleMessage()
      }

      return true
    } catch (error) {
      console.error('❌ 更新发送失败:', error)
      return false
    }
  }

  destroy(): void {
    console.log('🗑️ 销毁全局蓝牙服务')
    
    if (this.unwatchConnectedDevice) {
      this.unwatchConnectedDevice()
      this.unwatchConnectedDevice = null
    }

    this.bluetoothStore.resetState()
    this.isInitialized = false
    this.platformHook = null
  }

  // 获取服务状态（用于组件消费）
  get isActive(): boolean {
    return this.bluetoothStore.isBluetoothActive
  }

  get currentMethod(): string {
    return this.bluetoothStore.currentMethod
  }
}

// 全局单例实例
let globalBluetoothService: GlobalBluetoothService | null = null

/**
 * 获取全局蓝牙服务实例
 * 这是唯一的访问点
 */
export function getBluetoothService(): GlobalBluetoothService {
  if (!globalBluetoothService) {
    globalBluetoothService = new GlobalBluetoothService()
  }
  return globalBluetoothService
}

/**
 * 初始化全局蓝牙服务
 * 应该在应用启动时调用一次
 */
export async function initializeBluetoothService(): Promise<void> {
  const service = getBluetoothService()
  await service.initialize()
}

/**
 * 销毁全局蓝牙服务
 * 应该在应用卸载时调用
 */
export function destroyBluetoothService(): void {
  if (globalBluetoothService) {
    globalBluetoothService.destroy()
    globalBluetoothService = null
  }
}