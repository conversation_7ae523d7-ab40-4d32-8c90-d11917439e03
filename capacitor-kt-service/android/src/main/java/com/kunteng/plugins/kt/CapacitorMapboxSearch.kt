package com.kunteng.plugins.kt

import android.content.Context
import android.util.Log
import com.mapbox.search.SearchEngine
import com.mapbox.search.SearchEngineSettings
import com.mapbox.search.SearchOptions
import com.mapbox.search.SearchSelectionCallback
import com.mapbox.search.CategorySearchOptions
import com.mapbox.search.ReverseGeoOptions
import com.mapbox.search.ResponseInfo
import com.mapbox.search.SearchCallback
import com.mapbox.search.result.SearchResult
import com.mapbox.search.result.SearchSuggestion
import com.mapbox.search.common.AsyncOperationTask
import com.mapbox.search.ApiType
import com.mapbox.common.MapboxOptions
import com.mapbox.geojson.Point
import kotlinx.coroutines.*
import java.util.concurrent.Executor
import java.util.concurrent.Executors

class CapacitorMapboxSearch(private val context: Context? = null) {
    companion object {
        private const val TAG = "CapacitorMapboxSearch"
    }
    
    private var searchEngine: SearchEngine? = null
    private val executor: Executor = Executors.newSingleThreadExecutor()
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    init {
        initializeSearchEngines()
    }

    // 搜索结果回调接口
    interface SearchResultsCallback {
        fun onResults(results: List<SearchResult>)
        fun onError(error: String)
    }

    // 搜索建议回调接口
    interface SuggestionsCallback {
        fun onSuggestions(suggestions: List<SearchSuggestion>)
        fun onError(error: String)
    }
    
    private fun initializeSearchEngines() {
        try {
            // 从应用的资源或配置中获取 Mapbox 访问令牌
            val accessToken = getMapboxAccessToken()
            if (!accessToken.isNullOrEmpty()) {
                // Set the global Mapbox access token
                MapboxOptions.accessToken = accessToken
                
                // Create SearchEngineSettings with default parameters
                val settings = SearchEngineSettings()
                searchEngine = SearchEngine.createSearchEngineWithBuiltInDataProviders(
                    ApiType.GEOCODING,
                    settings
                )
                Log.d(TAG, "Mapbox Search engine initialized successfully")
            } else {
                Log.e(TAG, "Mapbox access token not found")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Mapbox Search engine", e)
        }
    }
    
    private fun getMapboxAccessToken(): String? {
        return try {
            // 尝试从多个地方获取访问令牌
            var token: String? = null
            
            context?.let { ctx ->
                // 1. 从应用元数据获取
                val ai = ctx.packageManager.getApplicationInfo(
                    ctx.packageName, 
                    android.content.pm.PackageManager.GET_META_DATA
                )
                ai.metaData?.let { metaData ->
                    token = metaData.getString("com.mapbox.token")
                        ?: metaData.getString("MAPBOX_ACCESS_TOKEN")
                }
                
                // 2. 从字符串资源获取
                if (token == null) {
                    try {
                        val resId = ctx.resources.getIdentifier(
                            "mapbox_access_token", 
                            "string", 
                            ctx.packageName
                        )
                        if (resId != 0) {
                            token = ctx.getString(resId)
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "No mapbox_access_token string resource found")
                    }
                }
            }
            
            token
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Mapbox access token", e)
            null
        }
    }
    
    fun searchPlaces(query: String, callback: SearchResultsCallback) {
        val engine = searchEngine
        if (engine == null) {
            callback.onError("Search engine not initialized")
            return
        }
        
        executor.execute {
            try {
                val options = SearchOptions.Builder().build()
                engine.search(query, options, object : SearchSelectionCallback {
                    override fun onSuggestions(suggestions: List<SearchSuggestion>, responseInfo: ResponseInfo) {
                        // 自动选择第一个建议获取详细结果
                        if (suggestions.isNotEmpty()) {
                            engine.select(suggestions[0], this)
                        } else {
                            callback.onResults(emptyList())
                        }
                    }
                    
                    override fun onResult(suggestion: SearchSuggestion, result: SearchResult, responseInfo: ResponseInfo) {
                        callback.onResults(listOf(result))
                    }
                    
                    override fun onResults(suggestion: SearchSuggestion, results: List<SearchResult>, responseInfo: ResponseInfo) {
                        callback.onResults(results)
                    }
                    
                    override fun onError(e: Exception) {
                        callback.onError("Search failed: ${e.message}")
                    }
                })
            } catch (e: Exception) {
                callback.onError("Search error: ${e.message}")
            }
        }
    }
    
    fun getPlaceSuggestions(query: String, callback: SuggestionsCallback) {
        val engine = searchEngine
        if (engine == null) {
            callback.onError("Search engine not initialized")
            return
        }
        
        executor.execute {
            try {
                val options = SearchOptions.Builder().build()
                engine.search(query, options, object : SearchSelectionCallback {
                    override fun onSuggestions(suggestions: List<SearchSuggestion>, responseInfo: ResponseInfo) {
                        callback.onSuggestions(suggestions)
                    }
                    
                    override fun onResult(suggestion: SearchSuggestion, result: SearchResult, responseInfo: ResponseInfo) {
                        // 不需要处理，这是建议搜索
                    }
                    
                    override fun onResults(suggestion: SearchSuggestion, results: List<SearchResult>, responseInfo: ResponseInfo) {
                        // 不需要处理，这是建议搜索
                    }
                    
                    override fun onError(e: Exception) {
                        callback.onError("Suggestions failed: ${e.message}")
                    }
                })
            } catch (e: Exception) {
                callback.onError("Suggestions error: ${e.message}")
            }
        }
    }
    
    fun reverseGeocode(latitude: Double, longitude: Double, callback: SearchResultsCallback) {
        val engine = searchEngine
        if (engine == null) {
            callback.onError("Search engine not initialized")
            return
        }
        
        executor.execute {
            try {
                val point = Point.fromLngLat(longitude, latitude)
                val options = ReverseGeoOptions.Builder(point).build()
                engine.search(options, object : SearchCallback {
                    override fun onResults(results: List<SearchResult>, responseInfo: ResponseInfo) {
                        callback.onResults(results)
                    }
                    
                    override fun onError(e: Exception) {
                        callback.onError("Reverse geocoding failed: ${e.message}")
                    }
                })
            } catch (e: Exception) {
                callback.onError("Reverse geocoding error: ${e.message}")
            }
        }
    }
    
    fun searchByCategory(category: String, latitude: Double, longitude: Double, callback: SearchResultsCallback) {
        val engine = searchEngine
        if (engine == null) {
            callback.onError("Search engine not initialized")
            return
        }

        executor.execute {
            try {
                val point = Point.fromLngLat(longitude, latitude)
                val options = CategorySearchOptions.Builder()
                    .proximity(point)
                    .build()

                engine.search(category, options, object : SearchCallback {
                    override fun onResults(results: List<SearchResult>, responseInfo: ResponseInfo) {
                        callback.onResults(results)
                    }

                    override fun onError(e: Exception) {
                        callback.onError("Category search failed: ${e.message}")
                    }
                })
            } catch (e: Exception) {
                callback.onError("Category search error: ${e.message}")
            }
        }
    }
    
    fun validateMapboxToken(): Boolean {
        val token = getMapboxAccessToken()
        return !token.isNullOrEmpty() && token.startsWith("pk.") && token.length > 20
    }
    
    fun selectSuggestion(suggestion: SearchSuggestion, callback: SearchResultsCallback) {
        val engine = searchEngine
        if (engine == null) {
            callback.onError("Search engine not initialized")
            return
        }
        
        executor.execute {
            try {
                engine.select(suggestion, object : SearchSelectionCallback {
                    override fun onSuggestions(suggestions: List<SearchSuggestion>, responseInfo: ResponseInfo) {
                        // 不需要处理，这是选择操作
                    }
                    
                    override fun onResult(suggestion: SearchSuggestion, result: SearchResult, responseInfo: ResponseInfo) {
                        callback.onResults(listOf(result))
                    }
                    
                    override fun onResults(suggestion: SearchSuggestion, results: List<SearchResult>, responseInfo: ResponseInfo) {
                        callback.onResults(results)
                    }
                    
                    override fun onError(e: Exception) {
                        callback.onError("Selection failed: ${e.message}")
                    }
                })
            } catch (e: Exception) {
                callback.onError("Selection error: ${e.message}")
            }
        }
    }
    
    // 清理资源
    fun cleanup() {
        scope.cancel()
    }
}
