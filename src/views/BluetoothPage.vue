<template>
  <ion-page ref="page">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/home"></ion-back-button>
        </ion-buttons>
        <ion-title>Bluetooth</ion-title>
        <ion-buttons v-if="!connectedDevice.isPaired" slot="end">
          <ion-button @click="handleManualRefresh" :disabled="scanning">
            <ion-icon slot="icon-only" :icon="refresh" :class="{ 'icon-refresh--on': scanning }"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="device-container" :fullscreen="true" v-if="!scanning">
      <!-- <ion-card v-if="connectedDevice.isPaired">
        <ion-card-header>
          <ion-card-title class="device-modal__title">
            {{ connectedDevice.name }}
          </ion-card-title>
          <ion-card-subtitle>Device Information</ion-card-subtitle>
        </ion-card-header>
        <ion-card-content>
          Device ID:{{ connectedDevice.deviceId }}
        </ion-card-content>
        <ion-button fill="clear" size="small" @click="presentAlert">Disconnect
        </ion-button>
      </ion-card> -->
      <template v-if="connectedDevice.isPaired">
        <ion-label class="device-label">Paired Devices</ion-label>
        <ion-list :inset="true" lines="full">
          <ion-item class="active" @click="presentAlert">
            <ion-icon slot="start" :icon="bicycle"></ion-icon>
            <ion-label>{{ connectedDevice.name }}</ion-label>
          </ion-item>
        </ion-list>
      </template>
      <template v-if="!connectedDevice.isPaired">
        <ion-label class="device-label">Available Devices</ion-label>
        <ion-list :inset="true" lines="full">
          <ion-item v-for="(item, index) in availableDevices" :key="index" @click="selectDevice(item)">
            <ion-icon slot="start" :icon="bluetooth"></ion-icon>
            <ion-label>{{ item.name }}</ion-label>
          </ion-item>
          <ion-item v-if="availableDevices.length === 0 && !scanning" lines="none">
            <ion-note v-if="isPlatform('ios')">
              No available Bluetooth devices found
            </ion-note>
            <ion-note v-else slot="start">
              No available Bluetooth devices found
            </ion-note>
          </ion-item>
        </ion-list>
      </template>
    </ion-content>
    <ion-content :fullscreen="true" v-else>
      <div class="loading-container">
        <ion-spinner name="crescent"></ion-spinner>
        <ion-label class="loading-container__text">Searching for available devices...</ion-label>
      </div>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  alertController,
  IonBackButton,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonItemDivider,
  IonItemGroup,
  IonLabel,
  IonList,
  IonNote,
  IonPage,
  IonSpinner,
  IonTitle,
  IonToolbar,
  isPlatform,
  loadingController,
} from "@ionic/vue";
import { bicycle, bluetooth, refresh } from "ionicons/icons";
import { Device, useBleStore } from "@/store/useBleStore";
import { useBluetoothLe } from "@/hooks/useBluetooth-le";
import { onMounted, onUnmounted, ref, shallowRef, watch } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useMessage } from "@/hooks/useMessage";
import { useToast } from "@/hooks/useToast";
import { useDisconnectEventBus } from "@/hooks/useDisconnectEventBus";

const store = useBleStore();
const { connectedDevice, availableDevices } = storeToRefs(store);
const { scan, scanning, connectBle, disConnectBle } = useBluetoothLe();
const { stopSendMessage } = useMessage();
const router = useRouter();
const { on } = useDisconnectEventBus();

// 🔧 扫描状态管理
let scanIntervalId: any = null;
const isScanningActive = ref(false);
const toast = useToast();

// 🔧 统一的扫描管理
const startScanning = async () => {
  if (isScanningActive.value || connectedDevice.value.isPaired) {
    console.log("🔧 扫描已在进行或设备已连接，跳过扫描");
    return;
  }

  console.log("🔍 开始蓝牙扫描");
  isScanningActive.value = true;

  // 立即执行一次扫描
  await scan();

  // 设置定时扫描（仅在未连接时）
  if (!connectedDevice.value.isPaired) {
    scanIntervalId = setInterval(async () => {
      if (connectedDevice.value.isPaired) {
        stopScanning();
        return;
      }
      console.log("🔄 定时扫描执行");
      await scan();
    }, 15000); // 15秒间隔
  }
};

const stopScanning = () => {
  if (scanIntervalId) {
    console.log("⏹️ 停止蓝牙扫描");
    clearInterval(scanIntervalId);
    scanIntervalId = null;
  }
  isScanningActive.value = false;
};

// 🔧 监听连接状态变化，自动管理扫描
watch(
  () => connectedDevice.value.isPaired,
  (isPaired) => {
    if (isPaired) {
      console.log("✅ 设备已连接，停止扫描");
      stopScanning();
    } else {
      console.log("📱 设备未连接，开始扫描");
      setTimeout(() => startScanning(), 1000); // 延迟1秒开始扫描
    }
  },
  { immediate: true }
);

// 🔧 页面生命周期管理
onMounted(() => {
  console.log("📱 蓝牙页面加载");
  if (!connectedDevice.value.isPaired) {
    startScanning();
  }
});

// 🔧 监听断开事件（仅在当前页面）
on(async () => {
  console.log("📱 收到断开事件，重新开始扫描");
  setTimeout(() => startScanning(), 2000); // 延迟2秒重新扫描
});

onUnmounted(() => {
  console.log("📱 蓝牙页面卸载，清理扫描");
  stopScanning();
});

// 🔧 手动刷新扫描
const handleManualRefresh = async () => {
  if (scanning.value || connectedDevice.value.isPaired) {
    console.log("🔧 正在扫描中或已连接，跳过手动刷新");
    return;
  }

  console.log("🔄 手动刷新扫描");
  stopScanning(); // 先停止当前扫描
  store.clearAvailableDevices(); // 清空设备列表

  setTimeout(() => {
    startScanning(); // 重新开始扫描
  }, 500);
};
const selectDevice = async (device: Device) => {
  console.log("🔗 选择设备连接:", device.name);
  stopScanning(); // 停止扫描
  await showConnectLoading();

  try {
    await connectBle(device);
    await connectLoading.dismiss();
    console.log("✅ 连接成功，返回上一页");
    router.back();
  } catch (e) {
    console.log("❌ 连接失败:", e);
    await connectLoading.dismiss();
    await toast.presentToast("Unmatched Bluetooth device");
    // 连接失败后重新开始扫描
    setTimeout(() => startScanning(), 1000);
  }
};
const alertButtons = [
  "Cancel",
  {
    text: "Okay",
    handler: async () => {
      alert.value?.dismiss();
      await showDisconnectLoading();

      try {
        console.log("🔌 手动断开蓝牙连接");
        await stopSendMessage();
        await disConnectBle(connectedDevice.value);
        disconnectLoading.value?.dismiss();
        console.log("✅ 断开连接成功");
      } catch (e) {
        console.log("❌ 断开连接失败:", e);
        disconnectLoading.value?.dismiss();
      }

      // 断开后自动开始扫描（由watch监听器自动处理）
      console.log("🔍 断开连接后将自动开始扫描");
    },
  },
];
const alert = ref<HTMLIonAlertElement>();
const presentAlert = async () => {
  alert.value = await alertController.create({
    header: "Alert",
    subHeader: "Do you want to disconnect the Bluetooth!",
    buttons: alertButtons,
  });

  await alert.value.present();
};
let connectLoading = {} as HTMLIonLoadingElement;
const showConnectLoading = async () => {
  connectLoading = await loadingController.create({
    message: "Connecting to Bluetooth device",
  });
  await connectLoading.present();
};
const disconnectLoading = shallowRef<HTMLIonLoadingElement>();
const showDisconnectLoading = async () => {
  disconnectLoading.value = await loadingController.create({
    message: "Disconnecting Bluetooth device",
  });

  await disconnectLoading.value.present();
};
</script>
<style lang="scss" scoped>
@keyframes refresh {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.icon-refresh--on {
  animation-name: refresh;
  animation-duration: 1s;
  animation-iteration-count: 1;
}

ion-header ion-toolbar:first-of-type {
  --border-width: 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .loading-container__text {
    margin-top: 8px;
    font-size: 14px;
    color: #757575;
    line-height: 20px;
  }
}

.device-container {
  --padding-top: 12px;

  .active {
    --color: var(--ion-color-primary);
  }

  ion-list {
    --background: #191A21;

    &.list-inset {
      margin-inline-start: 20px;
      margin-inline-end: 20px;
      margin-top: 8px;
      margin-bottom: 8px;
    }

    ion-item {
      --padding-start: 20px;
      --padding-end: 20px;
      --inner-padding-top: 6px;
      --inner-padding-bottom: 6px;
      --background: #191A21;
      --ion-item-border-color: #333333;
    }
  }

  .device-label {
    margin-left: 20px;
    font-size: 14px;
    color: #757575;
    line-height: 20px;
  }
}
</style>
