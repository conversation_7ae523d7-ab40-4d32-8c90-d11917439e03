import { watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useBleStore } from '@/store/useBleStore'
import { useBluetoothData } from './useBluetoothData'
import { useBluetoothSender } from './useBluetoothSender'

/**
 * 蓝牙管理器 Composable
 * 使用 Vue 3 最佳实践：组合多个 composables
 * 
 * 这个 composable 负责：
 * 1. 整合蓝牙数据和发送功能
 * 2. 监听连接状态变化
 * 3. 自动管理发送服务
 * 4. 提供统一的API接口
 */
export function useBluetoothManager() {
  // 组合其他 composables
  const bluetoothData = useBluetoothData()
  const bluetoothSender = useBluetoothSender()
  const bleStore = useBleStore()
  
  // 获取连接状态
  const { connectedDevice } = storeToRefs(bleStore)
  
  // 🔧 简化的连接状态监听（智能初始化检查）
  watch(
    () => connectedDevice.value?.isPaired,
    async (isConnected) => {
      if (isConnected) {
        console.log('🎉 蓝牙连接成功，智能等待初始化后启动发送服务')

        // 智能等待初始化完成
        const waitForInitAndStart = async () => {
          const maxRetries = 10; // 最多重试10次
          let retryCount = 0;

          const tryStart = async (): Promise<void> => {
            // 检查连接状态是否还有效
            if (!connectedDevice.value?.isPaired) {
              console.warn("⚠️ 设备连接状态已变化，取消启动");
              return;
            }

            try {
              // 确保蓝牙LE已初始化
              const { useBluetoothLe } = await import("@/hooks/useBluetooth-le");
              const { initialBle } = useBluetoothLe();

              console.log("🔧 确保蓝牙LE已初始化...");
              await initialBle();
              console.log("✅ 蓝牙LE初始化确认完成");

              console.log('🚀 启动发送服务')
              await bluetoothSender.startSending()
            } catch (error) {
              retryCount++;
              const errorMsg = error instanceof Error ? error.message : String(error);
              console.warn(`⚠️ 蓝牙初始化失败 (${retryCount}/${maxRetries}):`, errorMsg);

              if (retryCount < maxRetries) {
                console.log(`🔄 等待1秒后重试...`);
                setTimeout(tryStart, 1000);
              } else {
                console.error('❌ 蓝牙初始化重试次数已达上限');
              }
            }
          };

          // 延迟500ms后开始尝试，确保连接稳定
          setTimeout(tryStart, 500);
        };

        waitForInitAndStart();
      } else {
        console.log('📱 蓝牙断开，停止发送服务')
        await bluetoothSender.stopSending()
      }
    }
  )
  
  // 监听数据变化，自动发送最新数据
  watch(
    bluetoothData.writeData,
    async (newData) => {
      if (bluetoothSender.isSending.value) {
        console.log('🔄 数据已更新，发送最新数据')
        await bluetoothSender.sendData()
      }
    },
    { deep: true }
  )
  
  // 手动更新并发送数据（用于设置页面保存）
  const updateAndSend = async (): Promise<boolean> => {
    try {
      console.log('🔧 手动更新并发送蓝牙数据')
      
      // 数据会自动通过响应式系统更新
      // 这里只需要确保发送最新数据
      if (bluetoothSender.isSending.value) {
        return await bluetoothSender.sendData()
      } else {
        console.log('⚠️ 发送服务未运行，启动服务并发送')
        const started = await bluetoothSender.startSending()
        if (started) {
          return await bluetoothSender.sendData()
        }
        return false
      }
    } catch (error) {
      console.error('❌ 更新并发送失败:', error)
      return false
    }
  }
  
  // 强制重新发送当前数据
  const forceSend = async (): Promise<boolean> => {
    console.log('🔧 强制发送当前数据')
    return await bluetoothSender.sendData()
  }
  
  // 获取完整状态
  const getStatus = () => {
    return {
      // 连接状态
      isConnected: connectedDevice.value?.isPaired || false,
      
      // 发送状态
      ...bluetoothSender.getSendingStatus(),
      
      // 数据状态
      writeData: bluetoothData.writeData.value,
      currentSendData: bluetoothData.currentSendData.value,
      
      // 时间戳
      timestamp: Date.now()
    }
  }
  
  return {
    // 数据相关
    writeData: bluetoothData.writeData,
    currentSendData: bluetoothData.currentSendData,

    // 发送相关
    isSending: bluetoothSender.isSending,
    currentMethod: bluetoothSender.currentMethod,
    isAndroid: bluetoothSender.isAndroid,

    // 控制方法
    startSending: bluetoothSender.startSending,
    stopSending: bluetoothSender.stopSending,
    sendData: bluetoothSender.sendData,

    // 高级方法
    updateAndSend,
    forceSend,
    getStatus,

    // 数据更新方法
    updateNavigationData: bluetoothData.updateNavigationData,

    // 工具方法
    Int2Bytes: bluetoothData.Int2Bytes,
    calculateChecksum: bluetoothData.calculateChecksum
  }
}
