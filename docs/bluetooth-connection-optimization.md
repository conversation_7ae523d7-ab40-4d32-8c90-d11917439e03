# 蓝牙连接优化方案

## 🔧 问题分析

### 原始代码问题
```typescript
// ❌ 不合理的实现
initialBle().then(() => {
  setTimeout(async () => {
    await sendMessage();
  }, 1000);
});
```

**存在的问题：**
1. **时机不准确**: `initialBle()` 只是初始化蓝牙，不代表设备已连接
2. **固定延迟**: 使用 `setTimeout(1000)` 不可靠，无法保证连接完成
3. **平台无关**: 没有利用 `useSmartBluetoothMessage` 的智能平台选择
4. **重复调用**: 可能导致多次启动发送服务

## ✅ 优化后的解决方案

### 1. 智能蓝牙连接监听
```typescript
// ✅ 监听蓝牙连接状态变化，自动启动发送
const setupBluetoothConnectionListener = () => {
  watch(
    () => connectedDevice.value.isPaired,
    async (isPaired, wasPaired) => {
      if (isPaired && !wasPaired) {
        // 蓝牙刚刚连接成功
        console.log(`📱 使用 ${currentMethod.value} 发送方案`);
        
        setTimeout(async () => {
          if (connectedDevice.value.isPaired) {
            await sendMessage(); // 智能选择平台方案
          }
        }, 1500); // 确保连接稳定
      }
    }
  );
};
```

### 2. 平台智能选择
```typescript
// ✅ 使用智能蓝牙Hook，自动根据平台选择最佳方案
const smartBluetoothHook = useSmartBluetoothMessage();
const {
  sendMessage,        // 自动选择: Android=原生, iOS/Web=WebView
  currentMethod,      // 当前使用的方案类型
  updateSmartBluetoothData // 统一的数据更新方法
} = smartBluetoothHook;
```

### 3. 优化的生命周期管理
```typescript
onMounted(() => {
  // 🔧 设置蓝牙连接监听器
  setupBluetoothConnectionListener();
  
  // 🔧 只初始化蓝牙，发送将在连接成功后自动触发
  initialBle().catch((error) => {
    console.error('蓝牙初始化失败:', error);
  });
});

onIonViewDidEnter(async () => {
  if (connectedDevice.value.isPaired) {
    // 🔧 页面进入时检查发送状态，确保服务正常运行
    const isCurrentlySending = isServiceRunning.value;
    
    if (!isCurrentlySending) {
      await sendMessage(); // 重新启动
    } else {
      await updateSmartBluetoothData(); // 更新数据
    }
  }
});
```

## 🎯 核心改进

### 1. **事件驱动 vs 时间驱动**
- ❌ 原来: 固定延迟 `setTimeout(1000)`
- ✅ 现在: 监听连接状态变化 `watch(isPaired)`

### 2. **智能平台选择**
- ❌ 原来: 手动调用 `sendMessage()`
- ✅ 现在: 自动选择最佳方案 (Android=原生, iOS=WebView)

### 3. **连接状态感知**
- ❌ 原来: 不知道连接是否真正成功
- ✅ 现在: 监听 `connectedDevice.isPaired` 状态变化

### 4. **避免重复启动**
- ❌ 原来: 可能多次调用 `sendMessage()`
- ✅ 现在: 检查 `isServiceRunning` 状态

## 📱 平台支持

### Android (原生方案)
- ✅ 后台持续发送 (解决5分钟限制)
- ✅ 精确106ms间隔
- ✅ 系统级保活

### iOS/Web (WebView方案)
- ✅ 跨平台兼容
- ✅ 现有功能完整
- ⚠️ 熄屏5分钟限制

## 🔄 工作流程

1. **应用启动** → `onMounted()` → 初始化蓝牙 + 设置监听器
2. **蓝牙连接成功** → `watch(isPaired)` → 自动启动智能发送
3. **页面进入** → `onIonViewDidEnter()` → 检查状态 + 确保服务运行
4. **蓝牙断开** → `watch(isPaired)` → 自动停止发送

## 🎉 预期效果

1. **更可靠**: 基于真实连接状态，而非固定延迟
2. **更智能**: 自动选择最佳平台方案
3. **更稳定**: 避免重复启动和状态不一致
4. **更高效**: 减少不必要的延迟和检查
