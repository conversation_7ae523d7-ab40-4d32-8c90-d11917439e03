/**
 * 蓝牙单例管理器
 * 确保蓝牙相关的hooks只初始化一次，避免重复初始化问题
 */

import { ref, type Ref } from 'vue'

// 全局单例实例存储
const singletonInstances = new Map<string, any>()
const initializationFlags = new Map<string, boolean>()
const initializationPromises = new Map<string, Promise<any>>()

/**
 * 单例工厂函数
 * @param key 单例的唯一标识
 * @param factory 创建实例的工厂函数
 * @returns 单例实例
 */
export function createSingleton<T>(key: string, factory: () => T): T {
  if (singletonInstances.has(key)) {
    console.log(`🔄 复用现有${key}实例`)
    return singletonInstances.get(key)
  }

  console.log(`🔧 创建新的${key}实例`)
  const instance = factory()
  singletonInstances.set(key, instance)
  return instance
}

/**
 * 获取现有单例实例（如果不存在则返回null）
 * @param key 单例的唯一标识
 * @returns 单例实例或null
 */
export function getSingleton<T>(key: string): T | null {
  return singletonInstances.get(key) || null
}

/**
 * 异步单例工厂函数
 * 防止异步初始化时的竞争条件
 * @param key 单例的唯一标识
 * @param factory 异步创建实例的工厂函数
 * @returns 单例实例的Promise
 */
export async function createAsyncSingleton<T>(key: string, factory: () => Promise<T>): Promise<T> {
  // 如果已经有实例，直接返回
  if (singletonInstances.has(key)) {
    console.log(`🔄 复用现有${key}实例`)
    return singletonInstances.get(key)
  }

  // 如果正在初始化，等待现有的初始化完成
  if (initializationPromises.has(key)) {
    console.log(`⏳ 等待${key}初始化完成...`)
    return await initializationPromises.get(key)!
  }

  console.log(`🔧 开始异步创建${key}实例`)
  
  // 创建初始化Promise
  const initPromise = factory().then(instance => {
    singletonInstances.set(key, instance)
    initializationPromises.delete(key)
    console.log(`✅ ${key}实例创建完成`)
    return instance
  }).catch(error => {
    initializationPromises.delete(key)
    console.error(`❌ ${key}实例创建失败:`, error)
    throw error
  })

  initializationPromises.set(key, initPromise)
  return await initPromise
}

/**
 * 检查某个单例是否已经初始化
 * @param key 单例的唯一标识
 * @returns 是否已初始化
 */
export function hasSingleton(key: string): boolean {
  return singletonInstances.has(key)
}

/**
 * 清除单例实例（主要用于测试或重置）
 * @param key 单例的唯一标识，如果不提供则清除所有
 */
export function clearSingleton(key?: string): void {
  if (key) {
    singletonInstances.delete(key)
    initializationFlags.delete(key)
    initializationPromises.delete(key)
    console.log(`🗑️ 清除${key}单例实例`)
  } else {
    singletonInstances.clear()
    initializationFlags.clear()
    initializationPromises.clear()
    console.log(`🗑️ 清除所有单例实例`)
  }
}

/**
 * 获取所有已初始化的单例列表（用于调试）
 */
export function getInitializedSingletons(): string[] {
  return Array.from(singletonInstances.keys())
}

/**
 * 防止重复初始化的装饰器函数
 * @param key 唯一标识
 * @param initFn 初始化函数
 * @returns 初始化结果
 */
export async function preventDuplicateInit<T>(
  key: string, 
  initFn: () => Promise<T>
): Promise<T> {
  // 如果已经有完成的实例，直接返回
  if (singletonInstances.has(key)) {
    console.log(`🔄 ${key}已初始化完成，复用现有结果`)
    return singletonInstances.get(key)
  }

  // 如果正在初始化，等待完成
  if (initializationPromises.has(key)) {
    console.log(`⏳ ${key}正在初始化中，等待完成...`)
    return await initializationPromises.get(key)!
  }

  // 开始新的初始化
  console.log(`🚀 开始${key}初始化`)
  initializationFlags.set(key, true)
  
  const initPromise = initFn().then(result => {
    console.log(`✅ ${key}初始化完成`)
    singletonInstances.set(key, result)
    return result
  }).catch(error => {
    console.error(`❌ ${key}初始化失败:`, error)
    throw error
  }).finally(() => {
    initializationFlags.set(key, false)
    initializationPromises.delete(key)
  })
  
  initializationPromises.set(key, initPromise)
  return await initPromise
}

// 导出内部状态供调试使用（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  (window as any).__bluetoothSingletonDebug = {
    singletonInstances,
    initializationFlags,
    initializationPromises,
    getSingleton,
    hasSingleton,
    getInitializedSingletons
  }
}