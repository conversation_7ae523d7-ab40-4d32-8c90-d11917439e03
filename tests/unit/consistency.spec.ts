import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useSetting } from '../../src/hooks/useSetting'
import { useBluetoothData } from '../../src/composables/useBluetoothData'
import { useSettingStore } from '../../src/store/useSettingStore'
import { useDashboardStore } from '../../src/store/useDashboardStore'

// Mock Vue composition API
vi.mock('vue', () => ({
  ref: vi.fn((value) => ({ value })),
  onMounted: vi.fn((callback) => callback()),
  computed: vi.fn((getter) => ({ value: getter() })),
  watch: vi.fn(),
  readonly: vi.fn((value) => value)
}))

// Mock Pinia
vi.mock('pinia', () => ({
  storeToRefs: vi.fn((store) => {
    const refs = {}
    Object.keys(store).forEach(key => {
      if (typeof store[key] !== 'function') {
        refs[key] = { value: store[key] }
      }
    })
    return refs
  })
}))

// Mock runtime diagnostics
vi.mock('../../src/utils/runtimeDiagnostics', () => ({
  globalDiagnostics: {
    isMonitoring: { value: false },
    logDiagnostic: vi.fn()
  }
}))

// 共享的测试数据
const createTestData = () => ({
  settingStore: {
    dimension: 5,
    maxSpeed: 72,
    p1: 88,
    p2: 1,
    p3: 1,
    p4: 0,
    p5: 15,
    c1: 7,
    c2: 0,
    c3: 8,
    c4: 4,
    c5: 10,
    c7: 0,
    c12: 4,
    c13: 1,
    c14: 2,
    percent: 50,
    handlebarMaxSpeed: 20
  },
  dashboardStore: {
    gearPosition: -1,
    lightStatus: false,
    setGearPosition: vi.fn(),
    setLightStatus: vi.fn()
  }
})

// Mock stores
vi.mock('../../src/store/useSettingStore', () => ({
  useSettingStore: vi.fn()
}))

vi.mock('../../src/store/useDashboardStore', () => ({
  useDashboardStore: vi.fn()
}))

describe('useSetting vs useBluetoothData 一致性测试', () => {
  let mockSettingStore: any
  let mockDashboardStore: any

  beforeEach(() => {
    vi.clearAllMocks()

    const testData = createTestData()
    mockSettingStore = testData.settingStore
    mockDashboardStore = testData.dashboardStore

    // Mock stores
    vi.mocked(useSettingStore).mockReturnValue(mockSettingStore)
    vi.mocked(useDashboardStore).mockReturnValue(mockDashboardStore)
  })

  const compareWriteData = (setting: any, bluetoothData: any, testName: string) => {
    const settingData = setting.writeData.value
    const bluetoothDataArray = bluetoothData.writeData.value

    console.log(`\n=== ${testName} ===`)
    console.log('useSetting     :', settingData.map((b: number) => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    console.log('useBluetoothData:', bluetoothDataArray.map((b: number) => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))

    // 逐字节对比
    for (let i = 0; i < 18; i++) {
      if (settingData[i] !== bluetoothDataArray[i]) {
        console.log(`❌ 索引${i}: useSetting=${settingData[i]} (0x${settingData[i].toString(16)}), useBluetoothData=${bluetoothDataArray[i]} (0x${bluetoothDataArray[i].toString(16)})`)
      }
    }

    expect(bluetoothDataArray).toEqual(settingData)
  }

  it('默认参数下应该产生相同的writeData', () => {
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '默认参数测试')
  })

  it('修改P5参数后应该产生相同的writeData', () => {
    mockSettingStore.p5 = 25
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, 'P5=25测试')
  })

  it('修改P1参数后应该产生相同的writeData', () => {
    mockSettingStore.p1 = 100
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, 'P1=100测试')
  })

  it('修改速度和轮径后应该产生相同的writeData', () => {
    mockSettingStore.maxSpeed = 50
    mockSettingStore.dimension = 15
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '速度=50, 轮径=15测试')
  })

  it('修改C参数后应该产生相同的writeData', () => {
    mockSettingStore.c1 = 5
    mockSettingStore.c2 = 3
    mockSettingStore.c4 = 2
    mockSettingStore.c5 = 8
    mockSettingStore.c7 = 1
    mockSettingStore.c12 = 6
    mockSettingStore.c13 = 2
    mockSettingStore.c14 = 1
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, 'C参数修改测试')
  })

  it('修改档位和灯光状态后应该产生相同的writeData', () => {
    mockDashboardStore.gearPosition = 3
    mockDashboardStore.lightStatus = true
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '档位=3, 灯光=开启测试')
  })

  it('C3参数影响档位后应该产生相同的writeData', () => {
    mockSettingStore.c3 = 2
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, 'C3=2测试')
  })

  it('边界值测试应该产生相同的writeData', () => {
    mockSettingStore.p5 = 300  // 超出范围
    mockSettingStore.maxSpeed = 42  // 边界值
    mockSettingStore.dimension = 10  // 轮径扩展边界
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '边界值测试')
  })

  it('极值测试应该产生相同的writeData', () => {
    // 设置所有参数为最大值
    Object.keys(mockSettingStore).forEach(key => {
      if (typeof mockSettingStore[key] === 'number') {
        mockSettingStore[key] = 255
      }
    })
    mockDashboardStore.gearPosition = 5
    mockDashboardStore.lightStatus = true
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '极值测试')
  })

  it('零值测试应该产生相同的writeData', () => {
    // 设置所有参数为0
    Object.keys(mockSettingStore).forEach(key => {
      if (typeof mockSettingStore[key] === 'number') {
        mockSettingStore[key] = 0
      }
    })
    mockDashboardStore.gearPosition = 0
    mockDashboardStore.lightStatus = false
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '零值测试')
  })

  it('随机值测试应该产生相同的writeData', () => {
    // 生成随机测试数据
    mockSettingStore.dimension = Math.floor(Math.random() * 30) + 1
    mockSettingStore.maxSpeed = Math.floor(Math.random() * 100) + 10
    mockSettingStore.p1 = Math.floor(Math.random() * 256)
    mockSettingStore.p2 = Math.floor(Math.random() * 8)
    mockSettingStore.p3 = Math.floor(Math.random() * 8)
    mockSettingStore.p4 = Math.floor(Math.random() * 8)
    mockSettingStore.p5 = Math.floor(Math.random() * 256)
    mockSettingStore.c1 = Math.floor(Math.random() * 8)
    mockSettingStore.c2 = Math.floor(Math.random() * 8)
    mockSettingStore.c3 = Math.floor(Math.random() * 9)  // 0-8
    mockSettingStore.c4 = Math.floor(Math.random() * 8)
    mockSettingStore.c5 = Math.floor(Math.random() * 32)
    mockSettingStore.c7 = Math.floor(Math.random() * 8)
    mockSettingStore.c12 = Math.floor(Math.random() * 8)
    mockSettingStore.c13 = Math.floor(Math.random() * 64)
    mockSettingStore.c14 = Math.floor(Math.random() * 8)
    mockSettingStore.percent = Math.floor(Math.random() * 101)
    mockSettingStore.handlebarMaxSpeed = Math.floor(Math.random() * 100) + 1
    mockDashboardStore.gearPosition = Math.floor(Math.random() * 6)
    mockDashboardStore.lightStatus = Math.random() > 0.5
    
    const setting = useSetting()
    const bluetoothData = useBluetoothData()

    compareWriteData(setting, bluetoothData, '随机值测试')
  })

  describe('Int2Bytes函数一致性', () => {
    it('应该对相同输入产生相同输出', () => {
      const testValues = [0, 1, 15, 25, 100, 255, 256, 300, 999, NaN, null, undefined, '25', '100']

      testValues.forEach(value => {
        // 由于useSetting的Int2Bytes是内部函数，我们通过设置p5来间接测试
        mockSettingStore.p5 = value as any
        const settingResult = useSetting()
        const bluetoothResult = useBluetoothData()

        expect(bluetoothResult.writeData.value[0]).toBe(settingResult.writeData.value[0])
      })
    })
  })
})
