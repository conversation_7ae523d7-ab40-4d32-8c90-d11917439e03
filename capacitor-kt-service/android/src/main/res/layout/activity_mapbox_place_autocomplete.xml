<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="false"
    tools:context=".MapboxPlaceAutocompleteActivity">

    <!-- 地图视图 - 全屏显示，包括状态栏区域 -->
    <com.mapbox.maps.MapView
        android:id="@+id/map_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:mapbox_logoGravity="bottom"
        app:mapbox_scaleBarEnabled="false" />

    <!-- 搜索框容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/search_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="40dp"
        android:background="@drawable/search_box_background"
        android:elevation="8dp"
        android:paddingHorizontal="20dp"
        android:paddingVertical="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 搜索图标 -->
        <ImageView
            android:id="@+id/search_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_search"
            android:contentDescription="Search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 搜索输入框 -->
        <EditText
            android:id="@+id/query_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:autofillHints="@null"
            android:background="@null"
            android:hint="Search for places"
            android:inputType="text"
            android:textColor="#FFFFFF"
            android:textColorHint="#CCFFFFFF"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/search_icon"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 搜索结果列表 -->
    <com.mapbox.search.ui.view.SearchResultsView
        android:id="@+id/search_results_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="8dp"
        android:background="@android:color/white"
        android:clipToPadding="false"
        android:elevation="6dp"
        android:paddingTop="8dp"
        android:paddingBottom="22dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search_container" />

    <!-- 自定义地点详情卡片 -->
    <include
        android:id="@+id/custom_place_card"
        layout="@layout/custom_place_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>