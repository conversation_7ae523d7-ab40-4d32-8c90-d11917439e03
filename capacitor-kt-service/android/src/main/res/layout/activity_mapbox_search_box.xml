<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <EditText
        android:id="@+id/query_edit_text"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:hint="Where to?"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:textSize="16sp"
        android:inputType="text"
        android:imeOptions="actionSearch"
        android:singleLine="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray" />

    <com.mapbox.search.ui.view.SearchResultsView
        android:id="@+id/search_results_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:clipToPadding="false"
        android:elevation="4dp"
        android:paddingTop="8dp"
        android:paddingBottom="22dp"
        android:visibility="gone" />

</LinearLayout>