import Foundation
import MapboxSearch
import MapboxSearchUI
import CoreLocation

@objc public class CapacitorMapboxSearch: NSObject {
    
    // MARK: - Properties
    private var searchEngine: SearchEngine!
    private var categorySearchEngine: CategorySearchEngine!
    private var placeAutocomplete: PlaceAutocomplete!
    
    // MARK: - Initialization
    public override init() {
        super.init()
        setupSearchEngines()
    }
    
    private func setupSearchEngines() {
        if let accessToken = Bundle.main.object(forInfoDictionaryKey: "MBXAccessToken") as? String {
            searchEngine = SearchEngine(accessToken: accessToken)
            categorySearchEngine = CategorySearchEngine(accessToken: accessToken)
            placeAutocomplete = PlaceAutocomplete(accessToken: accessToken)
        } else {
            print("Warning: No Mapbox access token found")
        }
    }
    
    // MARK: - Public Methods
    // 搜索地点
    public func searchPlaces(query: String, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        // 新版本的 MapboxSearch API 使用不同的方法
        // 这里我们需要使用 SearchOptions 和不同的搜索方法
        let searchOptions = SearchOptions()
        searchEngine.search(query: query, options: searchOptions)

        // 注意：新版本的 API 可能需要不同的处理方式
        // 这里先提供一个基本的实现，可能需要进一步调整
        completion([])
    }
    
    // 获取地点建议
    public func getPlaceSuggestions(query: String, completion: @escaping ([PlaceAutocomplete.Suggestion]) -> Void) {
        guard let placeAutocomplete = placeAutocomplete else {
            completion([])
            return
        }

        // 新版本的 PlaceAutocomplete API 可能有不同的方法
        // 这里先提供一个基本的实现
        completion([])
    }
    
    // 根据建议获取详细信息
    public func getPlaceDetails(suggestion: PlaceAutocomplete.Suggestion, completion: @escaping (PlaceAutocomplete.Result?) -> Void) {
        guard let placeAutocomplete = placeAutocomplete else {
            completion(nil)
            return
        }

        // 新版本的 PlaceAutocomplete API 可能有不同的方法
        // 这里先提供一个基本的实现
        completion(nil)
    }
    
    // 反向地理编码
    public func reverseGeocode(coordinate: CLLocationCoordinate2D, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        // 新版本的反向地理编码 API 可能有不同的方法
        // 这里先提供一个基本的实现
        completion([])
    }
    
    // 类别搜索
    public func searchByCategory(category: String, around coordinate: CLLocationCoordinate2D, completion: @escaping ([SearchResult]) -> Void) {
        guard let categorySearchEngine = categorySearchEngine else {
            completion([])
            return
        }

        // 新版本的分类搜索 API 需要使用 SearchOptions
        let searchOptions = SearchOptions()
        categorySearchEngine.search(categoryName: category, options: searchOptions) { result in
            switch result {
            case .success(let searchResults):
                completion(searchResults)
            case .failure(let error):
                print("Category search error: \(error)")
                completion([])
            }
        }
    }
    
    // 检查Mapbox访问令牌
    public func validateMapboxToken() -> Bool {
        if let accessToken = Bundle.main.object(forInfoDictionaryKey: "MBXAccessToken") as? String,
           !accessToken.isEmpty {
            return true
        }
        return false
    }
    
    // 获取当前位置附近的地点
    public func getNearbyPlaces(coordinate: CLLocationCoordinate2D, radius: Double = 1000, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        // 新版本的附近搜索 API 需要使用 SearchOptions
        let searchOptions = SearchOptions()
        searchEngine.search(query: "", options: searchOptions)

        // 注意：新版本的 API 可能需要不同的处理方式
        // 这里先提供一个基本的实现
        completion([])
    }
}

// MARK: - CLLocationCoordinate2D Extension
extension CLLocationCoordinate2D {
    func distance(to coordinate: CLLocationCoordinate2D) -> Double {
        let location1 = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let location2 = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        return location1.distance(from: location2)
    }
}
