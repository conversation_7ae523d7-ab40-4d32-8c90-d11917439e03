import { ref } from 'vue';
import { isPlatform } from '@ionic/vue';
import { App } from '@capacitor/app';

export function useIOSBackgroundTimer() {
  const isBackground = ref(false);
  const isIOSTimerActive = ref(false);
  
  // iOS后台定时器管理
  const setupIOSBackgroundTimer = () => {
    if (!isPlatform('ios')) return;
    
    // 监听应用状态变化
    App.addListener('appStateChange', (state) => {
      isBackground.value = !state.isActive;
      console.log('iOS App state changed:', state.isActive ? 'foreground' : 'background');
    });
    
    // 监听页面可见性变化（额外保障）
    document.addEventListener('visibilitychange', () => {
      const hidden = document.hidden;
      isBackground.value = hidden;
      console.log('iOS Visibility changed:', hidden ? 'hidden' : 'visible');
    });
  };
  
  // 创建强制106ms定时器 - 不管前后台状态
  const createForcedTimer = (callback: () => void, interval: number = 106) => {
    if (!isPlatform('ios')) {
      // 非iOS平台直接使用标准定时器
      const intervalId = setInterval(callback, interval);
      return () => clearInterval(intervalId);
    }
    
    // iOS平台使用多种策略确保106ms间隔
    let mainTimer: number;
    let backupTimer: number;
    let rafTimer: number;
    let isActive = true;
    
    // 策略1: 标准setInterval
    mainTimer = window.setInterval(() => {
      if (isActive) callback();
    }, interval) as unknown as number;
    
    // 策略2: setTimeout递归调用（更精确）
    const recursiveTimeout = () => {
      if (!isActive) return;
      
      const startTime = performance.now();
      callback();
      
      const elapsed = performance.now() - startTime;
      const nextDelay = Math.max(0, interval - elapsed);
      
      backupTimer = setTimeout(recursiveTimeout, nextDelay) as unknown as number;
    };
    
    // 延迟启动备用定时器，避免重复调用
    setTimeout(recursiveTimeout, interval / 2);
    
    // 策略3: requestAnimationFrame高精度定时器
    let lastRAFTime = performance.now();
    const rafTick = (currentTime: number) => {
      if (!isActive) return;
      
      if (currentTime - lastRAFTime >= interval) {
        callback();
        lastRAFTime = currentTime;
      }
      
      rafTimer = requestAnimationFrame(rafTick);
    };
    
    rafTimer = requestAnimationFrame(rafTick);
    
    // 返回清理函数
    return () => {
      isActive = false;
      clearInterval(mainTimer);
      clearTimeout(backupTimer);
      cancelAnimationFrame(rafTimer);
    };
  };
  
  // 创建iOS优化的Web Worker定时器
  const createIOSWorkerTimer = (callback: () => void, interval: number = 106) => {
    const workerCode = `
      let intervalId;
      let timeoutId;
      
      // 双重定时器策略
      const startTimers = (interval) => {
        // 主定时器
        intervalId = setInterval(() => {
          self.postMessage('tick');
        }, interval);
        
        // 备用递归定时器
        const recursiveTick = () => {
          const start = performance.now();
          self.postMessage('tick');
          const elapsed = performance.now() - start;
          const nextDelay = Math.max(0, interval - elapsed);
          timeoutId = setTimeout(recursiveTick, nextDelay);
        };
        
        setTimeout(recursiveTick, interval / 2);
      };
      
      const stopTimers = () => {
        if (intervalId) clearInterval(intervalId);
        if (timeoutId) clearTimeout(timeoutId);
      };
      
      self.onmessage = function(e) {
        const { action, interval } = e.data;
        
        if (action === 'start') {
          stopTimers();
          startTimers(interval);
        } else if (action === 'stop') {
          stopTimers();
        }
      };
    `;
    
    const blob = new Blob([workerCode], { type: 'application/javascript' });
    const worker = new Worker(URL.createObjectURL(blob));
    
    let callCount = 0;
    worker.onmessage = () => {
      callCount++;
      // 过滤重复调用，确保精确间隔
      if (callCount % 2 === 1) {
        callback();
      }
    };
    
    worker.postMessage({ action: 'start', interval });
    
    return () => {
      worker.postMessage({ action: 'stop' });
      worker.terminate();
    };
  };
  
  // 🔧 修复：创建单一高精度定时器 - 避免多定时器混乱
  const createUltimateTimer = (callback: () => void, interval: number = 106) => {
    let callCount = 0;
    let isActive = true;
    let timerId: ReturnType<typeof setInterval> | null = null;

    // 🔧 使用单一定时器，避免多定时器冲突
    const wrappedCallback = () => {
      if (!isActive) return;

      callCount++;
      console.log(`🎯 iOS Timer tick #${callCount} at ${performance.now().toFixed(2)}ms`);
      callback();
    };

    // 🔧 优先使用最稳定的定时器策略
    try {
      // 首选：iOS Worker定时器（最稳定）
      const stopWorker = createIOSWorkerTimer(wrappedCallback, interval);
      console.log('🎯 使用 iOS Worker 定时器');

      return () => {
        isActive = false;
        stopWorker();
        console.log('🎯 iOS Worker 定时器已停止');
      };
    } catch (error) {
      console.warn('iOS Worker timer failed, fallback to setInterval:', error);
    }

    // 备选：单一 setInterval 定时器
    timerId = setInterval(() => {
      if (isActive) wrappedCallback();
    }, interval);

    console.log('🎯 使用 setInterval 定时器');

    // 返回停止函数
    return () => {
      isActive = false;
      if (timerId) {
        clearInterval(timerId);
        timerId = null;
      }
      console.log('🎯 setInterval 定时器已停止');
    };
  };
  
  return {
    isBackground,
    isIOSTimerActive,
    setupIOSBackgroundTimer,
    createForcedTimer,
    createIOSWorkerTimer,
    createUltimateTimer
  };
}