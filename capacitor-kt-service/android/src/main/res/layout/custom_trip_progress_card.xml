<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/trip_progress_card_background"
    android:orientation="vertical"
    android:clickable="true"
    android:focusable="true"
    android:clipToPadding="false"
    android:paddingBottom="0dp"
    android:theme="@style/KtMaterialTheme.NoActionBar">

    <!-- 顶部拖拽指示器 -->
    <View
        android:id="@+id/drag_indicator"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/drag_indicator_background" />

    <!-- 简介区域（绿色背景） -->
    <LinearLayout
        android:id="@+id/summary_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/minimized_content_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 文本区域容器（占据剩余空间），内部三列可换行 -->
        <LinearLayout
            android:id="@+id/summary_text_row"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:weightSum="3">

            <!-- 时间显示 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/time_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="29:32:06"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/time_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="Time"
                    android:textColor="#666666"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 距离显示 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/distance_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="19.3 km"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/distance_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="Distance"
                    android:textColor="#666666"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- ETA/到达时间显示 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/eta_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="18:45"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/eta_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:breakStrategy="balanced"
                    android:text="ETA"
                    android:textColor="#666666"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 控制按钮 (暂停/播放) - 圆形 MaterialButton 图标按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/control_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/btn_control_selector"
            android:contentDescription="暂停/继续"
            android:elevation="0dp"
            android:gravity="center"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:padding="0dp"
            android:stateListAnimator="@null"
            app:backgroundTint="@android:color/black"
            app:cornerRadius="20dp"
            app:icon="@drawable/ic_pause"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="24dp"
            app:iconTint="@color/kt_summary_green" />

    </LinearLayout>

    <!-- 详细信息区域 -->
    <LinearLayout
        android:id="@+id/details_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical">

        <!-- 第一行：海拔和速度 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <!-- 海拔卡片 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/detail_card_background"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:id="@+id/altitude_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3.5 m"
                    android:textColor="#FFFFFF"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/altitude_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Altitude"
                    android:textColor="#AAAAAA"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- 速度卡片 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/detail_card_background"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:id="@+id/speed_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="32.6 km/h"
                    android:textColor="#FFFFFF"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/speed_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Speed"
                    android:textColor="#AAAAAA"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 第二行：极速和匀速 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <!-- 极速卡片 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/detail_card_background"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:id="@+id/max_speed_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="43.8 km/h"
                    android:textColor="#FFFFFF"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/max_speed_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Extreme speed"
                    android:textColor="#AAAAAA"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- 匀速卡片 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/detail_card_background"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:id="@+id/avg_speed_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="30.2 km/h"
                    android:textColor="#FFFFFF"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/avg_speed_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Uniform speed"
                    android:textColor="#AAAAAA"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 底部按钮区域 -->
        <LinearLayout
            android:id="@+id/bottom_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="0dp"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal"
            android:weightSum="3">

            <!-- 投屏开关 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/cast_toggle_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:minWidth="0dp"
                android:text="Cast"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:textStyle="bold"
                app:backgroundTint="@color/cast_button_bg" />

            <!-- 最小化导航 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/nav_minimize_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:minWidth="0dp"
                android:text="Minimize"
                android:textColor="#000000"
                android:textSize="12sp"
                android:textStyle="bold"
                app:backgroundTint="@color/minimize_button_bg" />

            <!-- 结束导航 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/end_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:minWidth="0dp"
                android:text="EXIT"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:textStyle="bold"
                app:backgroundTint="@color/end_button_bg" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
