<template>
  <ion-app>
    <Suspense>
      <ion-router-outlet />
    </Suspense>
    
    <!-- 蓝牙服务调试面板 (仅在开发模式下显示) -->
    <BluetoothServiceDebugPanel v-if="isDevelopment" />
  </ion-app>
</template>

<script lang="ts" setup>
import { IonApp, IonRouterOutlet, isPlatform } from '@ionic/vue'
import { KeepAwake } from '@capacitor-community/keep-awake'
import { StatusBar, Style } from '@capacitor/status-bar'
import { useBleStore } from '@/store/useBleStore'
import { useDisconnectEventBus } from '@/hooks/useDisconnectEventBus'
import { useToast } from '@/hooks/useToast'
import { useDashboardStore } from '@/store/useDashboardStore'
import { getBluetoothService } from '@/services/bluetoothService'
import { onMounted, onUnmounted, computed } from 'vue'
const store = useBleStore()
const { resetDashboard } = useDashboardStore()
const { on } = useDisconnectEventBus()
const toast = useToast()

// 计算属性
const isDevelopment = computed(() => process.env.NODE_ENV === 'development')

on(async () => {
  // 🔧 监听设备断开事件
  console.log("📱 App.vue: 收到蓝牙断开事件");
  await toast.presentToast('Bluetooth disconnected')
  store.updateConnectedDevicePairedStatus(false)
  store.clearAvailableDevices()
  
  // 🔧 使用全局蓝牙服务
  try {
    const bluetoothService = getBluetoothService()
    await bluetoothService.stopSending()
  } catch (error) {
    console.warn("⚠️ 停止蓝牙发送失败:", error)
  }
  
  resetDashboard()

  // 🔧 不在App.vue中触发扫描，由具体页面管理
  console.log("📱 App.vue: 蓝牙断开处理完成，扫描由页面管理");
})

onMounted(async () => {
  await keepAwake()
  await setupStatusBar()
})

onUnmounted(() => {
  allowSleep()
})

const keepAwake = async () => {
  await KeepAwake.keepAwake()
}

const allowSleep = async () => {
  await KeepAwake.allowSleep()
}

// 设置状态栏样式
const setupStatusBar = async () => {
  if (isPlatform('android')) {
    try {
      // 设置状态栏背景色（与标题栏一致的黑色）
      await StatusBar.setBackgroundColor({ color: '#000000' })

      // 设置状态栏文字为白色（适合黑色背景）
      await StatusBar.setStyle({ style: Style.Light })

      // 确保状态栏不覆盖内容
      await StatusBar.setOverlaysWebView({ overlay: false })

      console.log('✅ 状态栏样式设置成功')
    } catch (error) {
      console.error('❌ 设置状态栏样式失败:', error)
    }
  }
}
</script>
