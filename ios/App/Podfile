source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
# 禁用 CDN，强制使用 Git 源
ENV['COCOAPODS_DISABLE_STATS'] = 'true'
require_relative '../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '14.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCommunityBluetoothLe', :path => '../../node_modules/@capacitor-community/bluetooth-le'
  pod 'CapacitorCommunityKeepAwake', :path => '../../node_modules/@capacitor-community/keep-awake'
  pod 'CapacitorApp', :path => '../../node_modules/@capacitor/app'
  pod 'CapacitorHaptics', :path => '../../node_modules/@capacitor/haptics'
  pod 'CapacitorKeyboard', :path => '../../node_modules/@capacitor/keyboard'
  pod 'CapacitorScreenOrientation', :path => '../../node_modules/@capacitor/screen-orientation'
  pod 'CapacitorStatusBar', :path => '../../node_modules/@capacitor/status-bar'
  pod 'CapacitorKtService', :path => '../../capacitor-kt-service'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

post_install do |installer|
  assertDeploymentTarget(installer)

  # 修复 Xcode 16 兼容性问题
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # 修复 DT_TOOLCHAIN_DIR 问题
      ['LIBRARY_SEARCH_PATHS', 'FRAMEWORK_SEARCH_PATHS', 'HEADER_SEARCH_PATHS'].each do |setting|
        if config.build_settings[setting]
          config.build_settings[setting] = config.build_settings[setting].map do |path|
            if path.is_a?(String)
              path.gsub('$(DT_TOOLCHAIN_DIR)', '$(TOOLCHAIN_DIR)')
            else
              path
            end
          end
        end
      end

      # 确保最低部署目标
      config.build_settings.delete 'IPHONEOS_DEPLOYMENT_TARGET'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'

      # 修复其他可能的 DT_TOOLCHAIN_DIR 引用
      config.build_settings.each do |key, value|
        if value.is_a?(String) && value.include?('$(DT_TOOLCHAIN_DIR)')
          config.build_settings[key] = value.gsub('$(DT_TOOLCHAIN_DIR)', '$(TOOLCHAIN_DIR)')
        elsif value.is_a?(Array)
          config.build_settings[key] = value.map do |item|
            if item.is_a?(String) && item.include?('$(DT_TOOLCHAIN_DIR)')
              item.gsub('$(DT_TOOLCHAIN_DIR)', '$(TOOLCHAIN_DIR)')
            else
              item
            end
          end
        end
      end

      # 为 Mapbox 相关目标添加特殊处理
      if target.name.include?('Mapbox')
        # 禁用 bitcode
        config.build_settings['ENABLE_BITCODE'] = 'NO'
        # 设置 Swift 版本
        config.build_settings['SWIFT_VERSION'] = '5.0'
        # 添加编译器标志
        config.build_settings['OTHER_SWIFT_FLAGS'] = '$(inherited) -Xfrontend -warn-long-function-bodies=100'

        # 特别处理 MapboxSearch 和 MapboxSearchUI 的 DT_TOOLCHAIN_DIR 问题
        if target.name == 'MapboxSearch' || target.name == 'MapboxSearchUI'
          config.build_settings['LIBRARY_SEARCH_PATHS'] = config.build_settings['LIBRARY_SEARCH_PATHS']&.map do |path|
            path.gsub('$(DT_TOOLCHAIN_DIR)', '$(TOOLCHAIN_DIR)')
          end
        end
      end
    end
  end
end
