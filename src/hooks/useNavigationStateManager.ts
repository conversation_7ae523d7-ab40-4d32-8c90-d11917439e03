import { ref, computed } from 'vue'
import { useNavigation } from '@/hooks/useNavigation'

// 全局导航状态管理器
const isNavigationActive = ref(false)
const navigationStartTime = ref<number | null>(null)
const currentNavigationData = ref<any>(null)

export function useNavigationStateManager() {
  const navigation = useNavigation()

  // 计算导航持续时间
  const navigationDuration = computed(() => {
    if (!navigationStartTime.value) return 0
    return Date.now() - navigationStartTime.value
  })

  // 启动导航
  const startNavigation = (data?: any) => {
    console.log('🚀 全局导航状态：启动导航')
    isNavigationActive.value = true
    navigationStartTime.value = Date.now()
    currentNavigationData.value = data

    // 触发导航启动事件
    const event = new CustomEvent('navigationStateChanged', {
      detail: { 
        active: true, 
        action: 'start',
        data: data,
        timestamp: navigationStartTime.value
      }
    })
    window.dispatchEvent(event)
  }

  // 停止导航并清理数据
  const stopNavigation = (reason: string = 'manual') => {
    console.log('🛑 全局导航状态：停止导航，原因:', reason)
    
    // 清理导航数据
    navigation.stopSendNavigationProgress()
    
    // 重置状态
    isNavigationActive.value = false
    navigationStartTime.value = null
    currentNavigationData.value = null

    // 触发导航停止事件
    const event = new CustomEvent('navigationStateChanged', {
      detail: { 
        active: false, 
        action: 'stop',
        reason: reason,
        timestamp: Date.now()
      }
    })
    window.dispatchEvent(event)

    // 触发数据清理事件
    const clearEvent = new CustomEvent('navigationDataCleared', {
      detail: { reason: reason }
    })
    window.dispatchEvent(clearEvent)

    console.log('✅ 导航状态已清理')
  }

  // 更新导航进度
  const updateNavigationProgress = (data: any) => {
    if (!isNavigationActive.value) {
      console.warn('⚠️ 导航未激活，忽略进度更新')
      return
    }

    currentNavigationData.value = data
    navigation.sendNavigationProgress(data)

    // 触发进度更新事件
    const event = new CustomEvent('navigationProgressUpdated', {
      detail: { data: data, timestamp: Date.now() }
    })
    window.dispatchEvent(event)
  }

  // 完成导航
  const completeNavigation = () => {
    console.log('🎯 全局导航状态：导航完成')
    stopNavigation('completed')
  }

  // 取消导航
  const cancelNavigation = () => {
    console.log('❌ 全局导航状态：导航取消')
    stopNavigation('cancelled')
  }

  // 获取导航状态信息
  const getNavigationStatus = () => {
    return {
      isActive: isNavigationActive.value,
      startTime: navigationStartTime.value,
      duration: navigationDuration.value,
      currentData: currentNavigationData.value
    }
  }

  // 强制清理（用于应急情况）
  const forceCleanup = () => {
    console.log('🧹 强制清理导航状态')
    navigation.stopSendNavigationProgress()
    navigation.resetWriteData()
    isNavigationActive.value = false
    navigationStartTime.value = null
    currentNavigationData.value = null

    // 触发强制清理事件
    const event = new CustomEvent('navigationDataCleared', {
      detail: { reason: 'force_cleanup' }
    })
    window.dispatchEvent(event)
  }

  return {
    // 状态
    isNavigationActive: computed(() => isNavigationActive.value),
    navigationDuration,
    currentNavigationData: computed(() => currentNavigationData.value),
    
    // 方法
    startNavigation,
    stopNavigation,
    updateNavigationProgress,
    completeNavigation,
    cancelNavigation,
    getNavigationStatus,
    forceCleanup
  }
}

// 创建全局单例
let globalNavigationStateManager: ReturnType<typeof useNavigationStateManager> | null = null

export function getGlobalNavigationStateManager() {
  if (!globalNavigationStateManager) {
    globalNavigationStateManager = useNavigationStateManager()
  }
  return globalNavigationStateManager
}
