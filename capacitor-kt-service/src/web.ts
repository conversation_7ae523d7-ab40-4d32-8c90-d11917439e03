import { WebPlugin } from '@capacitor/core';

import type {
  CapacitorKtService,
  BluetoothSendingConfig,
  BluetoothSendingState,
  BluetoothSendingStats,
  BluetoothServiceStatus,
  MapboxNavigationOptions,
  MapboxNavigationResult,
  NavigationPermissionStatus,
  MapboxSearchOptions,
  MapboxSearchResult,
  MapboxSuggestionResult,
  MapboxCoordinateOptions,
  MapboxCategorySearchOptions,
  MapboxOpenOptions,
} from './definitions';

export class CapacitorKtWeb extends WebPlugin implements CapacitorKtService {
  
  // =================== 蓝牙后台通信功能 ===================

  async startBluetoothForegroundService(): Promise<void> {
    console.log('startBluetoothForegroundService - Web implementation (not supported)');
    throw new Error('Bluetooth foreground service is not supported on web platform');
  }

  async stopBluetoothForegroundService(): Promise<void> {
    console.log('stopBluetoothForegroundService - Web implementation (no-op)');
  }

  async requestIgnoreBatteryOptimizations(): Promise<void> {
    console.log('requestIgnoreBatteryOptimizations - Web implementation (no-op)');
    console.warn('电池优化功能仅在Android平台可用');
  }

  async isBluetoothServiceRunning(): Promise<{ isRunning: boolean }> {
    console.log('isBluetoothServiceRunning - Web implementation');
    return { isRunning: false };
  }

  async updateBluetoothNotification(options: { title?: string; message?: string }): Promise<void> {
    console.log('updateBluetoothNotification - Web implementation (no-op)', options);
  }

  async checkDozeMode(): Promise<{ isInDozeMode: boolean; isIgnoringBatteryOptimizations: boolean }> {
    console.log('checkDozeMode - Web implementation');
    return { 
      isInDozeMode: false, 
      isIgnoringBatteryOptimizations: true 
    };
  }

  async openBatteryOptimizationSettings(): Promise<void> {
    console.log('openBatteryOptimizationSettings - Web implementation (no-op)');
    console.warn('电池优化设置功能仅在Android平台可用');
  }

  async sendKeepAlive(): Promise<void> {
    console.log('sendKeepAlive - Web implementation (no-op)');
  }

  async getDeviceInfo(): Promise<{
    manufacturer: string;
    model: string;
    androidVersion: string;
    sdkVersion: number;
    hasAggressivePowerManagement: boolean;
  }> {
    console.log('getDeviceInfo - Web implementation');
    return {
      manufacturer: 'Web',
      model: 'Browser',
      androidVersion: 'N/A',
      sdkVersion: 0,
      hasAggressivePowerManagement: false
    };
  }

  async openAutoStartSettings(): Promise<void> {
    console.log('openAutoStartSettings - Web implementation (no-op)');
    console.warn('自启动设置功能仅在Android平台可用');
  }

  async startNativeBluetoothSending(options: BluetoothSendingConfig): Promise<void> {
    console.log('startNativeBluetoothSending - Web implementation (no-op)', options);
    console.warn('原生蓝牙发送功能仅在移动平台可用');
  }

  async stopNativeBluetoothSending(): Promise<void> {
    console.log('stopNativeBluetoothSending - Web implementation (no-op)');
  }

  async updateBluetoothSendData(options: { data: number[] }): Promise<void> {
    console.log('updateBluetoothSendData - Web implementation (no-op)', options.data);
  }

  async isNativeBluetoothSending(): Promise<BluetoothSendingState> {
    console.log('isNativeBluetoothSending - Web implementation');
    return {
      isActive: false,
      lastSendTime: 0,
      sendCount: 0,
      errorCount: 0
    };
  }

  async getBluetoothSendingStats(): Promise<BluetoothSendingStats> {
    console.log('getBluetoothSendingStats - Web implementation');
    return {
      totalSent: 0,
      successCount: 0,
      errorCount: 0,
      averageInterval: 0,
      isConnected: false
    };
  }

  async getCurrentBluetoothSendData(): Promise<{ success: boolean; data?: number[]; error?: string }> {
    console.log('getCurrentBluetoothSendData - Web implementation');
    return {
      success: false,
      error: '原生蓝牙数据获取功能仅在移动平台可用'
    };
  }

  async getMirrorState(): Promise<{ enabled: boolean }> {
    console.log('getMirrorState - Web implementation');
    return { enabled: false };
  }

  async reconnectBluetoothDevice(): Promise<void> {
    console.log('reconnectBluetoothDevice - Web implementation (no-op)');
  }

  async startSendingAfterConnection(): Promise<{ success: boolean; message?: string }> {
    console.log('startSendingAfterConnection - Web implementation');
    return {
      success: true,
      message: 'Web implementation: 连接后发送启动模拟成功'
    };
  }

  async getServiceInitializationStatus(): Promise<BluetoothServiceStatus> {
    console.log('getServiceInitializationStatus - Web implementation');
    return {
      isServiceRunning: false,
      bluetoothManagerInitialized: false,
      lastSendingStatusCached: false,
      lastSendingStatsCached: false,
      bluetoothManagerDetails: 'Web平台不支持蓝牙服务',
      timestamp: Date.now(),
      error: '蓝牙服务功能仅在移动平台可用'
    };
  }

  // =================== Mapbox导航功能 ===================

  async showMapboxNavigation(options: MapboxNavigationOptions): Promise<MapboxNavigationResult> {
    console.log('showMapboxNavigation - Web implementation (not supported)', options);
    console.warn('Mapbox导航功能仅在移动平台可用');
    return {
      status: 'failure',
      type: 'on_failure',
      data: 'Mapbox导航功能在Web平台不支持'
    };
  }

  async hideNavigationFragment(): Promise<{ success: boolean }> {
    console.log('hideNavigationFragment - Web implementation (not supported)');
    console.warn('导航界面控制功能仅在移动平台可用');
    return { success: false };
  }

  async showNavigationFragment(): Promise<{ success: boolean }> {
    console.log('showNavigationFragment - Web implementation (not supported)');
    console.warn('导航界面控制功能仅在移动平台可用');
    return { success: false };
  }

  async getNavigationFragmentVisibility(): Promise<{ isVisible: boolean }> {
    console.log('getNavigationFragmentVisibility - Web implementation (not supported)');
    console.warn('导航界面状态查询功能仅在移动平台可用');
    return { isVisible: false };
  }

  async getNavigationHistory(): Promise<any> {
    console.log('getNavigationHistory - Web implementation (not supported)');
    console.warn('导航历史功能仅在移动平台可用');
    return null;
  }

  async requestNavigationPermissions(): Promise<NavigationPermissionStatus> {
    console.log('requestNavigationPermissions - Web implementation (not supported)');
    console.warn('导航权限功能仅在移动平台可用');
    return {
      location: 'granted'
    };
  }

  async checkNavigationPermissions(): Promise<NavigationPermissionStatus> {
    console.log('checkNavigationPermissions - Web implementation (not supported)');
    return {
      location: 'granted'
    };
  }

  // =================== Mapbox搜索功能 ===================

  async searchMapboxPlaces(options: MapboxSearchOptions): Promise<MapboxSearchResult> {
    console.log('searchMapboxPlaces - Web implementation (not supported)', options);
    console.warn('Mapbox地点搜索功能仅在移动平台可用');
    return { results: [] };
  }

  async getMapboxPlaceSuggestions(options: MapboxSearchOptions): Promise<MapboxSuggestionResult> {
    console.log('getMapboxPlaceSuggestions - Web implementation (not supported)', options);
    console.warn('Mapbox地点建议功能仅在移动平台可用');
    return { suggestions: [] };
  }

  async reverseGeocodeMapbox(options: MapboxCoordinateOptions): Promise<MapboxSearchResult> {
    console.log('reverseGeocodeMapbox - Web implementation (not supported)', options);
    console.warn('Mapbox反向地理编码功能仅在移动平台可用');
    return { results: [] };
  }

  async searchMapboxByCategory(options: MapboxCategorySearchOptions): Promise<MapboxSearchResult> {
    console.log('searchMapboxByCategory - Web implementation (not supported)', options);
    console.warn('Mapbox类别搜索功能仅在移动平台可用');
    return { results: [] };
  }

  async validateMapboxToken(): Promise<{ isValid: boolean }> {
    console.log('validateMapboxToken - Web implementation (not supported)');
    console.warn('Mapbox令牌验证功能仅在移动平台可用');
    return { isValid: false };
  }

  async openMapboxMap(options: MapboxOpenOptions): Promise<void> {
    console.log('openMapboxMap - Web implementation (not supported)', options);
    console.warn('Mapbox地图功能仅在移动平台可用');
  }

  async openMapboxSearchBox(): Promise<void> {
    console.log('openMapboxSearchBox - Web implementation (not supported)');
    console.warn('Mapbox搜索功能仅在移动平台可用');
  }

  async openMapboxAutocomplete(): Promise<void> {
    console.log('openMapboxAutocomplete - Web implementation (not supported)');
    console.warn('Mapbox自动完成功能仅在移动平台可用');
  }
}
