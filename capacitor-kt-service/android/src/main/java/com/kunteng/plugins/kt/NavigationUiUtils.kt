package com.kunteng.plugins.kt

import android.content.Context
import android.view.View
import android.view.ViewTreeObserver
import androidx.appcompat.app.AlertDialog
import com.mapbox.maps.ImageHolder
import com.mapbox.maps.MapView
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.extension.style.expressions.dsl.generated.interpolate
import com.mapbox.maps.extension.style.expressions.dsl.generated.literal
import com.mapbox.maps.extension.style.expressions.dsl.generated.zoom
import com.mapbox.maps.plugin.LocationPuck2D
import com.mapbox.maps.plugin.PuckBearing
import com.mapbox.maps.plugin.locationcomponent.location
import com.mapbox.navigation.ui.maps.camera.data.MapboxNavigationViewportDataSource
import com.mapbox.navigation.base.formatter.UnitType
import java.util.Locale
import com.mapbox.navigation.ui.maps.location.NavigationLocationProvider
import kotlin.math.max

/**
 * Shared UI helpers to keep Activity and DialogFragment DRY.
 */
object MapViewPuckHelper {
    fun configure(mapView: MapView, navigationLocationProvider: NavigationLocationProvider, context: Context) {
        val arrowResId = context.resources.getIdentifier("mapbox_user_puck_icon", "drawable", context.packageName)
        val shadowResId = context.resources.getIdentifier("mapbox_user_icon_shadow", "drawable", context.packageName)
        val scaleExpr = interpolate {
            linear()
            zoom()
            stop { literal(0.0); literal(0.6) }
            stop { literal(20.0); literal(1.0) }
        }.toJson()

        mapView.location.apply {
            setLocationProvider(navigationLocationProvider)
            updateSettings {
                puckBearing = PuckBearing.COURSE
                puckBearingEnabled = true
                enabled = true
                locationPuck = if (arrowResId != 0 || shadowResId != 0) {
                    LocationPuck2D(
                        bearingImage = if (arrowResId != 0) ImageHolder.from(arrowResId) else null,
                        shadowImage = if (shadowResId != 0) ImageHolder.from(shadowResId) else null,
                        scaleExpression = scaleExpr
                    )
                } else {
                    LocationPuck2D(scaleExpression = scaleExpr)
                }
            }
        }
    }
}

class SpeedDisplayHelper(
    private val unitProvider: () -> UnitType,
    private val smoothingAlpha: Double = 0.2
) {
    private var smoothedSpeedMetersPerSecond: Double? = null

    private enum class SpeedUnit { KMH, MPH }
    private fun preferredSpeedUnit(): SpeedUnit =
        if (unitProvider.invoke() == UnitType.IMPERIAL) SpeedUnit.MPH else SpeedUnit.KMH

    fun format(speedMetersPerSecond: Double): String {
        val current = max(0.0, speedMetersPerSecond)
        val prev = smoothedSpeedMetersPerSecond
        val smoothed = if (prev == null) current else (1 - smoothingAlpha) * prev + smoothingAlpha * current
        smoothedSpeedMetersPerSecond = smoothed
        return when (preferredSpeedUnit()) {
            SpeedUnit.KMH -> String.format(Locale.getDefault(), "%.1f km/h", smoothed * 3.6)
            SpeedUnit.MPH -> String.format(Locale.getDefault(), "%.1f mph", smoothed * 2.23694)
        }
    }

    /**
     * Formats without applying smoothing, suitable for aggregate values like average and max.
     */
    fun formatRaw(speedMetersPerSecond: Double): String {
        val clamped = max(0.0, speedMetersPerSecond)
        return when (preferredSpeedUnit()) {
            SpeedUnit.KMH -> String.format(Locale.getDefault(), "%.1f km/h", clamped * 3.6)
            SpeedUnit.MPH -> String.format(Locale.getDefault(), "%.1f mph", clamped * 2.23694)
        }
    }
}

object ViewportPaddingHelper {
    /**
     * Attach dynamic bottom padding that accounts for a bottom sheet overlay.
     * Returns a function to detach listeners.
     */
    fun attach(
        rootView: View,
        sheetView: View,
        viewportDataSource: MapboxNavigationViewportDataSource,
        baseOverview: EdgeInsets,
        baseFollowing: EdgeInsets,
        overlayScale: Double = 0.7
    ): () -> Unit {
        fun updatePadding() {
            val rootHeight = rootView.height
            val sheetTop = sheetView.top
            if (rootHeight > 0 && sheetTop > 0) {
                val overlayPx = (rootHeight - sheetTop).coerceAtLeast(0)
                val eff = (overlayPx * overlayScale).coerceAtLeast(0.0)
                viewportDataSource.overviewPadding = EdgeInsets(
                    baseOverview.top,
                    baseOverview.left,
                    baseOverview.bottom + eff,
                    baseOverview.right
                )
                viewportDataSource.followingPadding = EdgeInsets(
                    baseFollowing.top,
                    baseFollowing.left,
                    baseFollowing.bottom + eff,
                    baseFollowing.right
                )
                viewportDataSource.evaluate()
            }
        }

        val layoutListener = View.OnLayoutChangeListener { _, _, _, _, _, _, _, _, _ -> updatePadding() }
        val globalListener = ViewTreeObserver.OnGlobalLayoutListener { updatePadding() }

        sheetView.addOnLayoutChangeListener(layoutListener)
        rootView.viewTreeObserver.addOnGlobalLayoutListener(globalListener)

        return {
            sheetView.removeOnLayoutChangeListener(layoutListener)
            rootView.viewTreeObserver.removeOnGlobalLayoutListener(globalListener)
        }
    }
}

class AltitudeDisplayHelper(
    private val unitProvider: () -> UnitType
) {
    fun format(altitudeMeters: Double): String {
        val unit = unitProvider.invoke()
        return if (unit == UnitType.IMPERIAL) {
            String.format(Locale.getDefault(), "%.0f ft", altitudeMeters * 3.28084)
        } else {
            String.format(Locale.getDefault(), "%.0f m", altitudeMeters)
        }
    }
}

object MirrorUiHelper {
    fun showMirrorConfirmation(
        context: Context,
        currentlyEnabled: Boolean,
        onConfirmed: (Boolean) -> Unit
    ) {
        val title = "投屏确认"
        val message = if (currentlyEnabled) "是否关闭投屏？" else "是否开启投屏？"
        val positive = if (currentlyEnabled) "关闭" else "开启"
        AlertDialog.Builder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(positive) { _, _ ->
                onConfirmed(!currentlyEnabled)
            }
            .setNegativeButton("取消", null)
            .show()
    }
}


