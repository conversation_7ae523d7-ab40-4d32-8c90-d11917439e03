<template>
  <ion-page class="mapbox-search-test-page">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/my"></ion-back-button>
        </ion-buttons>
        <ion-title>MapBox 搜索测试</ion-title>
      </ion-toolbar>
    </ion-header>
    
    <ion-content class="ion-padding">
      <!-- 页面说明 -->
      <div class="page-header">
        <ion-icon class="header-icon" :icon="searchOutline"></ion-icon>
        <ion-text>
          <h2>MapBox 搜索功能测试</h2>
          <p>测试原生MapBox搜索组件和API功能</p>
        </ion-text>
      </div>

      <!-- 搜索UI测试区域 -->
      <div class="test-section">
        <ion-text class="section-title">
          <h3>🎯 搜索UI测试</h3>
        </ion-text>
        
        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="layersOutline"></ion-icon>
              原生搜索框
            </ion-card-title>
            <ion-card-subtitle>使用MapBox官方SearchResultsView组件</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <p>完整的搜索体验，支持实时搜索、历史记录和收藏夹功能</p>
            <ion-button 
              expand="block" 
              @click="testSearchBox"
              :disabled="isLoading"
            >
              <ion-icon slot="start" :icon="searchOutline"></ion-icon>
              打开搜索框
            </ion-button>
          </ion-card-content>
        </ion-card>

        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="listOutline"></ion-icon>
              地点自动完成
            </ion-card-title>
            <ion-card-subtitle>自定义UI实现的地点建议功能</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <p>简化的地点搜索建议界面，支持实时建议显示</p>
            <ion-button 
              expand="block" 
              @click="testAutocomplete"
              :disabled="isLoading"
              color="secondary"
            >
              <ion-icon slot="start" :icon="locationOutline"></ion-icon>
              打开自动完成
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- API测试区域 -->
      <div class="test-section">
        <ion-text class="section-title">
          <h3>🔧 API功能测试</h3>
        </ion-text>

        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="globeOutline"></ion-icon>
              基础搜索API
            </ion-card-title>
            <ion-card-subtitle>测试searchMapboxPlaces方法</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-item class="search-input-item">
              <ion-input 
                v-model="searchQuery"
                placeholder="输入搜索关键词 (例如: New York)"
                clear-input
              ></ion-input>
            </ion-item>
            <ion-button 
              expand="block" 
              @click="testSearchAPI"
              :disabled="isLoading || !searchQuery.trim()"
              color="tertiary"
            >
              <ion-icon slot="start" :icon="searchOutline"></ion-icon>
              搜索地点
            </ion-button>
          </ion-card-content>
        </ion-card>

        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="bulbOutline"></ion-icon>
              建议搜索API
            </ion-card-title>
            <ion-card-subtitle>测试getMapboxPlaceSuggestions方法</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-item class="search-input-item">
              <ion-input 
                v-model="suggestionQuery"
                placeholder="输入关键词获取建议 (例如: Paris)"
                clear-input
              ></ion-input>
            </ion-item>
            <ion-button 
              expand="block" 
              @click="testSuggestionsAPI"
              :disabled="isLoading || !suggestionQuery.trim()"
              color="warning"
            >
              <ion-icon slot="start" :icon="bulbOutline"></ion-icon>
              获取建议
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- 其他API测试 -->
      <div class="test-section">
        <ion-text class="section-title">
          <h3>🌍 其他API测试</h3>
        </ion-text>

        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="navigateOutline"></ion-icon>
              反向地理编码
            </ion-card-title>
            <ion-card-subtitle>根据坐标获取地址信息</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div class="coordinate-inputs">
              <ion-item class="coordinate-item">
                <ion-label position="stacked">纬度</ion-label>
                <ion-input 
                  v-model="latitude"
                  type="number"
                  placeholder="40.7128"
                ></ion-input>
              </ion-item>
              <ion-item class="coordinate-item">
                <ion-label position="stacked">经度</ion-label>
                <ion-input 
                  v-model="longitude"
                  type="number" 
                  placeholder="-74.0060"
                ></ion-input>
              </ion-item>
            </div>
            <ion-button 
              expand="block" 
              @click="testReverseGeocode"
              :disabled="isLoading || !latitude || !longitude"
              color="success"
            >
              <ion-icon slot="start" :icon="navigateOutline"></ion-icon>
              反向地理编码
            </ion-button>
          </ion-card-content>
        </ion-card>

        <ion-card class="test-card">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="restaurantOutline"></ion-icon>
              分类搜索
            </ion-card-title>
            <ion-card-subtitle>搜索特定类别的地点</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-item class="search-input-item">
              <ion-label position="stacked">分类</ion-label>
              <ion-select v-model="selectedCategory" placeholder="选择分类">
                <ion-select-option value="restaurant">餐厅</ion-select-option>
                <ion-select-option value="gas_station">加油站</ion-select-option>
                <ion-select-option value="hospital">医院</ion-select-option>
                <ion-select-option value="hotel">酒店</ion-select-option>
                <ion-select-option value="atm">ATM</ion-select-option>
              </ion-select>
            </ion-item>
            <ion-button 
              expand="block" 
              @click="testCategorySearch"
              :disabled="isLoading || !selectedCategory"
              color="danger"
            >
              <ion-icon slot="start" :icon="restaurantOutline"></ion-icon>
              分类搜索
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- 测试结果显示区域 -->
      <div v-if="lastResult" class="result-section">
        <ion-text class="section-title">
          <h3>📊 最近测试结果</h3>
        </ion-text>
        <ion-card class="result-card">
          <ion-card-header>
            <ion-card-title>{{ lastResult.title }}</ion-card-title>
            <ion-card-subtitle>{{ lastResult.timestamp }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <pre class="result-content">{{ lastResult.content }}</pre>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- 加载指示器 -->
      <ion-loading 
        :is-open="isLoading"
        message="测试中..."
      ></ion-loading>

    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonIcon,
  IonText,
  IonInput,
  IonItem,
  IonLabel,
  IonSelect,
  IonSelectOption,
  IonLoading,
  alertController,
  toastController
} from '@ionic/vue';
import {
  searchOutline,
  locationOutline,
  globeOutline,
  bulbOutline,
  navigateOutline,
  restaurantOutline,
  layersOutline,
  listOutline
} from 'ionicons/icons';
import { CapacitorKtService } from 'capacitor-kt-service';

// 响应式数据
const isLoading = ref(false);
const searchQuery = ref('New York');
const suggestionQuery = ref('Paris');
const latitude = ref(40.7128);
const longitude = ref(-74.0060);
const selectedCategory = ref('');
const lastResult = ref<{
  title: string;
  content: string;
  timestamp: string;
} | null>(null);

// 更新测试结果
const updateResult = (title: string, content: any) => {
  lastResult.value = {
    title,
    content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
    timestamp: new Date().toLocaleString()
  };
};

// 显示成功消息
const showSuccess = async (title: string, message: string) => {
  const alert = await alertController.create({
    header: title,
    message,
    buttons: ['确定']
  });
  await alert.present();
};

// 显示错误消息
const showError = async (title: string, error: any) => {
  const alert = await alertController.create({
    header: title,
    message: `错误信息: ${error.message || error}`,
    buttons: ['确定']
  });
  await alert.present();
};

// 测试搜索框UI
const testSearchBox = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试MapBox搜索框...');
    const result = await CapacitorKtService.openMapboxSearchBox();
    console.log('搜索框结果:', result);
    
    updateResult('搜索框测试结果', result);
    
    if (result.cancelled) {
      const toast = await toastController.create({
        message: '用户取消了搜索',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    } else {
      await showSuccess('搜索成功', `
        地点名称: ${result.placeName || '未知'}<br>
        地址: ${result.address || '无'}<br>
        坐标: ${result.latitude}, ${result.longitude}
      `);
    }
  } catch (error: any) {
    console.error('MapBox搜索框测试失败:', error);
    updateResult('搜索框测试错误', error);
    await showError('搜索框测试失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 测试自动完成UI
const testAutocomplete = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试MapBox自动完成...');
    const result = await CapacitorKtService.openMapboxAutocomplete();
    console.log('自动完成结果:', result);
    
    updateResult('自动完成测试结果', result);
    
    if (result.cancelled) {
      const toast = await toastController.create({
        message: '用户取消了搜索',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    } else {
      await showSuccess('选择成功', `
        地点名称: ${result.placeName || '未知'}<br>
        地址: ${result.address || '无'}<br>
        坐标: ${result.latitude}, ${result.longitude}
      `);
    }
  } catch (error: any) {
    console.error('MapBox自动完成测试失败:', error);
    updateResult('自动完成测试错误', error);
    await showError('自动完成测试失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 测试搜索API
const testSearchAPI = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试搜索API...', searchQuery.value);
    const result = await CapacitorKtService.searchMapboxPlaces({
      query: searchQuery.value
    });
    console.log('搜索API结果:', result);
    
    updateResult('搜索API测试结果', result);
    
    if (result.results && result.results.length > 0) {
      const firstResult = result.results[0];
      await showSuccess('搜索API成功', `
        找到 ${result.results.length} 个结果<br>
        第一个结果:<br>
        名称: ${firstResult.name || '无'}<br>
        地址: ${firstResult.address || '无'}<br>
        坐标: ${firstResult.latitude}, ${firstResult.longitude}
      `);
    } else {
      const toast = await toastController.create({
        message: '搜索API未返回结果',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    }
  } catch (error: any) {
    console.error('搜索API测试失败:', error);
    updateResult('搜索API测试错误', error);
    await showError('搜索API测试失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 测试建议API
const testSuggestionsAPI = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试建议API...', suggestionQuery.value);
    const result = await CapacitorKtService.getMapboxPlaceSuggestions({
      query: suggestionQuery.value
    });
    console.log('建议API结果:', result);
    
    updateResult('建议API测试结果', result);
    
    if (result.suggestions && result.suggestions.length > 0) {
      await showSuccess('建议API成功', `找到 ${result.suggestions.length} 个建议`);
    } else {
      const toast = await toastController.create({
        message: '建议API未返回结果',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    }
  } catch (error: any) {
    console.error('建议API测试失败:', error);
    updateResult('建议API测试错误', error);
    await showError('建议API测试失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 测试反向地理编码
const testReverseGeocode = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试反向地理编码...', latitude.value, longitude.value);
    const result = await CapacitorKtService.reverseGeocodeMapbox({
      latitude: Number(latitude.value),
      longitude: Number(longitude.value)
    });
    console.log('反向地理编码结果:', result);
    
    updateResult('反向地理编码测试结果', result);
    
    if (result.results && result.results.length > 0) {
      const firstResult = result.results[0];
      await showSuccess('反向地理编码成功', `
        找到 ${result.results.length} 个结果<br>
        第一个结果:<br>
        名称: ${firstResult.name || '无'}<br>
        地址: ${firstResult.address || '无'}
      `);
    } else {
      const toast = await toastController.create({
        message: '反向地理编码未返回结果',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    }
  } catch (error: any) {
    console.error('反向地理编码测试失败:', error);
    updateResult('反向地理编码测试错误', error);
    await showError('反向地理编码测试失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 测试分类搜索
const testCategorySearch = async () => {
  isLoading.value = true;
  try {
    console.log('开始测试分类搜索...', selectedCategory.value);
    const result = await CapacitorKtService.searchMapboxByCategory({
      category: selectedCategory.value,
      latitude: Number(latitude.value),
      longitude: Number(longitude.value)
    });
    console.log('分类搜索结果:', result);
    
    updateResult('分类搜索测试结果', result);
    
    if (result.results && result.results.length > 0) {
      await showSuccess('分类搜索成功', `
        找到 ${result.results.length} 个 ${selectedCategory.value} 结果
      `);
    } else {
      const toast = await toastController.create({
        message: '分类搜索未返回结果',
        duration: 2000,
        position: 'middle',
        color: 'warning'
      });
      await toast.present();
    }
  } catch (error: any) {
    console.error('分类搜索测试失败:', error);
    updateResult('分类搜索测试错误', error);
    await showError('分类搜索测试失败', error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.mapbox-search-test-page {
  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--ion-color-light);
    border-radius: 12px;

    .header-icon {
      font-size: 48px;
      color: var(--ion-color-primary);
      margin-right: 16px;
    }

    h2 {
      margin: 0 0 8px 0;
      color: var(--ion-color-primary);
    }

    p {
      margin: 0;
      color: var(--ion-color-medium);
    }
  }

  .test-section {
    margin-bottom: 32px;

    .section-title {
      margin-left: 8px;
      margin-bottom: 16px;

      h3 {
        color: var(--ion-color-dark);
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }
    }

    .test-card {
      margin-bottom: 16px;

      ion-card-header {
        ion-card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
        }

        ion-card-subtitle {
          font-size: 12px;
          margin-top: 4px;
        }
      }

      .search-input-item {
        margin-bottom: 16px;
        --background: var(--ion-color-light);
        --border-radius: 8px;
      }

      .coordinate-inputs {
        margin-bottom: 16px;

        .coordinate-item {
          margin-bottom: 8px;
          --background: var(--ion-color-light);
          --border-radius: 8px;
        }
      }
    }
  }

  .result-section {
    margin-top: 32px;

    .section-title {
      margin-left: 8px;
      margin-bottom: 16px;

      h3 {
        color: var(--ion-color-success);
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }
    }

    .result-card {
      .result-content {
        background: var(--ion-color-light);
        padding: 12px;
        border-radius: 8px;
        font-size: 12px;
        line-height: 1.4;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>