# 蓝牙自动发送Hook使用指南

## 🎯 概述

`useBluetoothAutoSender` 是一个全局蓝牙自动发送管理Hook，负责监听蓝牙连接状态变化并自动启动/停止数据发送。

## 🏗️ 架构设计

### 全局插件初始化
```typescript
// src/main.ts
import bluetoothAutoSenderPlugin from './plugins/bluetoothAutoSender'

const app = createApp(App)
  .use(bluetoothAutoSenderPlugin) // 🔧 全局启用
```

### Hook结构
```typescript
// src/hooks/useBluetoothAutoSender.ts
export function useBluetoothAutoSender() {
  return {
    // 主要控制方法
    startBluetoothConnectionListener,    // 启动监听器
    stopBluetoothConnectionListener,     // 停止监听器
    checkAndRestoreBluetoothSending,     // 检查并恢复发送状态
    
    // 状态信息
    isListenerActive,                    // 监听器是否活跃
    isServiceRunning,                    // 发送服务是否运行
    currentMethod,                       // 当前使用的发送方案
    
    // 调试方法
    getBluetoothAutoSenderStatus,        // 获取完整状态
    debugInfo                            // 调试信息
  }
}
```

## 🔄 工作流程

### 1. 应用启动
```typescript
// 自动执行（通过插件）
bluetoothAutoSender.startBluetoothConnectionListener()
```

### 2. 蓝牙连接监听
```typescript
// 监听连接状态变化
watch(() => connectedDevice.value?.isPaired, async (isPaired, wasPaired) => {
  if (isPaired && !wasPaired) {
    // 🎉 连接成功 → 自动启动发送
    await handleBluetoothConnected()
  } else if (!isPaired && wasPaired) {
    // 📱 连接断开 → 自动停止发送
    await handleBluetoothDisconnected()
  }
})
```

### 3. 智能平台选择
```typescript
// 自动根据平台选择最佳方案
const { sendMessage, currentMethod } = useSmartBluetoothMessage()

// Android: 原生蓝牙发送 (后台持续发送)
// iOS/Web: WebView方案 (跨平台兼容)
console.log(`📱 使用 ${currentMethod.value} 发送方案`)
```

## 📱 页面集成

### HomePage.vue 示例
```typescript
<script setup lang="ts">
import { useBluetoothAutoSender } from '@/hooks/useBluetoothAutoSender'
import { useSmartBluetoothMessage } from '@/hooks/useSmartBluetoothMessage'

// 🔧 全局蓝牙自动发送管理器
const { checkAndRestoreBluetoothSending } = useBluetoothAutoSender()

// 🔧 页面内手动操作保留直接访问
const { sendMessage, stopSendMessage } = useSmartBluetoothMessage()

onMounted(() => {
  // 🔧 只需初始化蓝牙，发送由全局监听器自动处理
  initialBle().catch(console.error)
})

onIonViewDidEnter(async () => {
  // 🔧 页面进入时检查并恢复发送状态
  await checkAndRestoreBluetoothSending()
})
</script>
```

## 🎛️ API 参考

### 主要方法

#### `startBluetoothConnectionListener()`
启动全局蓝牙连接监听器
- **返回**: `Function` - 停止监听的方法
- **用途**: 应用启动时自动调用

#### `checkAndRestoreBluetoothSending()`
检查并恢复蓝牙发送状态
- **返回**: `Promise<void>`
- **用途**: 页面进入时确保服务正常运行

#### `getBluetoothAutoSenderStatus()`
获取完整状态信息
```typescript
{
  isListenerActive: boolean,        // 监听器是否活跃
  isBluetoothConnected: boolean,    // 蓝牙是否连接
  isServiceRunning: boolean,        // 发送服务是否运行
  currentMethod: string,            // 当前发送方案
  lastConnectionState: boolean      // 上次连接状态
}
```

### 状态属性

#### `isListenerActive`
- **类型**: `Ref<boolean>`
- **描述**: 监听器是否正在运行

#### `isServiceRunning`
- **类型**: `ComputedRef<boolean>`
- **描述**: 蓝牙发送服务是否正在运行

#### `currentMethod`
- **类型**: `ComputedRef<string>`
- **描述**: 当前使用的发送方案 ('native' | 'webview')

## 🔧 使用场景

### 1. 全局自动化（推荐）
```typescript
// ✅ 应用启动时自动启用，无需手动干预
// 插件会自动处理所有连接状态变化
```

### 2. 页面状态检查
```typescript
// ✅ 页面进入时检查状态
onIonViewDidEnter(async () => {
  await checkAndRestoreBluetoothSending()
})
```

### 3. 手动操作
```typescript
// ✅ 用户手动操作时直接调用
const { sendMessage, stopSendMessage } = useSmartBluetoothMessage()

const handleUserAction = async () => {
  await sendMessage() // 立即发送
}
```

## 🎉 优势

### 1. **全局自动化**
- ✅ 应用启动时自动启用
- ✅ 无需在每个页面手动设置
- ✅ 统一的连接状态管理

### 2. **智能平台选择**
- ✅ Android: 原生蓝牙发送
- ✅ iOS/Web: WebView方案
- ✅ 自动选择最佳方案

### 3. **状态感知**
- ✅ 实时监听连接状态变化
- ✅ 自动启动/停止发送服务
- ✅ 避免重复启动

### 4. **简化页面代码**
- ✅ 页面只需关注业务逻辑
- ✅ 蓝牙连接管理完全自动化
- ✅ 减少重复代码

## 🚀 迁移指南

### 从旧代码迁移
```typescript
// ❌ 旧代码
initialBle().then(() => {
  setTimeout(async () => {
    await sendMessage()
  }, 1000)
})

// ✅ 新代码
onMounted(() => {
  initialBle().catch(console.error)
  // 发送由全局监听器自动处理
})

onIonViewDidEnter(async () => {
  await checkAndRestoreBluetoothSending()
})
```

## 🔍 调试

### 查看状态
```typescript
const bluetoothAutoSender = useBluetoothAutoSender()
console.log(bluetoothAutoSender.getBluetoothAutoSenderStatus())
console.log(bluetoothAutoSender.debugInfo())
```

### 日志输出
Hook会自动输出详细的日志信息，包括：
- 🎯 监听器启动/停止
- 🔄 连接状态变化
- 🚀 发送服务启动/停止
- 📱 平台方案选择
