/**
 * 蓝牙数据反解析工具
 * 从发送的蓝牙数据中解析出导航相关数据和设置数据
 */

import { Directions } from '@/const/directions'

// 🔧 轮径映射表 (根据 bike.const.ts 中的 DimensionSize)
const DimensionMap: { [key: number]: string } = {
  0: '16寸',    // inch_16 = 0
  1: '18寸',    // inch_18 = 1
  2: '20寸',    // inch_20 = 2
  3: '22寸',    // inch_22 = 3
  4: '24寸',    // inch_24 = 4
  5: '26寸',    // inch_26 = 5
  6: '700C',    // c700 = 6
  7: '28寸',    // inch_28 = 7
  10: '12寸',   // inch_12 = 10
  11: '14寸',   // inch_14 = 11
  12: '8寸',    // inch_8 = 12
  13: '10寸',   // inch_10 = 13
  14: '6寸',    // inch_6 = 14
  15: '5寸',    // inch_5 = 15
  17: '29寸'    // inch_29 = 17
}

export interface ParsedBluetoothData {
  // 协议信息
  protocol: {
    dataBytes: number[]           // 数据字节（0-11）
    isValid: boolean
    endByte: number
    checksum5: {                  // 字节5校验和（基础数据）
      calculated: number
      actual: number
      isValid: boolean
    }
    checksum16: {                 // 字节16校验和（包含导航数据）
      calculated: number
      actual: number
      isValid: boolean
    }
  }
  
  // 设置数据
  settings: {
    p5: number                    // 字节0: P5参数
    gearAndLight: {               // 字节1: 档位和灯光
      gear: number
      gearName: string
      lightOn: boolean
    }
    speedAndDimension: {          // 字节2: 速度和轮径
      speed: number               // 实际限速 (原始值 + 10)
      dimensionCode: number       // 轮径编码值
      dimensionName: string       // 轮径名称 (如: "700C", "26寸")
      dimensionMode: number       // 轮径模式 (0: <10, 1: >=10)
    }
    p1: number                    // 字节3: P1参数
    combinedParams: {             // 字节4: P2+P3+P4+限速+轮径扩展
      p2: number
      p3: number
      p4: number
      speedLimit: number
      dimensionExpand: boolean
    }
    c1c2: {                       // 字节6: C1C2参数
      c1: number
      c2: number
    }
    c5c14: {                      // 字节7: C5C14参数
      c5: number
      c14: number
    }
    c4c7c12: {                    // 字节8: C4C7C12参数
      c4: number
      c7: number
      c12: number
    }
    handlebarMaxSpeed: number     // 字节9: 把手最大速度
    c13: number                   // 字节10: C13参数
    percent: number               // 字节11: 百分比
  }
  
  // 导航数据
  navigation: {
    direction: {
      code: number
      name: string
    }
    mirror: {
      enabled: boolean
    }
    singleDistance: {
      rule: number
      ruleName: string
      high: number
      low: number
      effectiveNumber: number
      actualDistance: number
    }
    totalDistance: {
      rule: number
      ruleName: string
      high: number
      low: number
      effectiveNumber: number
      actualDistance: number
    }
  }
  
  // 原始数据
  rawData: number[]
  
  // 解析状态
  parseStatus: {
    success: boolean
    errors: string[]
    warnings: string[]
  }
}

/**
 * 解析蓝牙数据包
 */
export function parseBluetoothData(data: number[]): ParsedBluetoothData {
  const result: ParsedBluetoothData = {
    protocol: {
      dataBytes: [],
      isValid: false,
      endByte: 0,
      checksum5: { calculated: 0, actual: 0, isValid: false },
      checksum16: { calculated: 0, actual: 0, isValid: false }
    },
    settings: {
      p5: 0,
      gearAndLight: { gear: 0, gearName: '', lightOn: false },
      speedAndDimension: { speed: 0, dimensionCode: 0, dimensionName: '', dimensionMode: 0 },
      p1: 0,
      combinedParams: { p2: 0, p3: 0, p4: 0, speedLimit: 0, dimensionExpand: false },
      c1c2: { c1: 0, c2: 0 },
      c5c14: { c5: 0, c14: 0 },
      c4c7c12: { c4: 0, c7: 0, c12: 0 },
      handlebarMaxSpeed: 0,
      c13: 0,
      percent: 0
    },
    navigation: {
      direction: { code: 0, name: '' },
      mirror: { enabled: false },
      singleDistance: { rule: 0, ruleName: '', high: 0, low: 0, effectiveNumber: 0, actualDistance: 0 },
      totalDistance: { rule: 0, ruleName: '', high: 0, low: 0, effectiveNumber: 0, actualDistance: 0 }
    },
    rawData: [...data],
    parseStatus: { success: true, errors: [], warnings: [] }
  }

  try {
    // 1. 基本验证
    if (!data || !Array.isArray(data) || data.length !== 18) {
      result.parseStatus.success = false
      result.parseStatus.errors.push(`数据长度错误: 期望18字节，实际${data?.length || 0}字节`)
      return result
    }

    // 2. 解析协议信息
    parseProtocolInfo(data, result)

    // 3. 解析设置数据
    parseSettingsData(data, result)

    // 4. 解析导航数据
    parseNavigationData(data, result)

    console.log('🔍 蓝牙数据解析完成:', result)

  } catch (error: any) {
    result.parseStatus.success = false
    result.parseStatus.errors.push(`解析失败: ${error.message}`)
  }

  return result
}

/**
 * 解析协议信息
 */
function parseProtocolInfo(data: number[], result: ParsedBluetoothData) {
  // 🔧 修复：没有协议头概念，只有数据字节、协议尾和校验和
  // 数据字节 (字节0-11: 与控制器通讯的协议)
  result.protocol.dataBytes = data.slice(0, 12)

  // 🔧 协议有效性验证：只检查结束位（唯一固定的字节）
  // 数据字节0-11都可能因设置而变化，只有结束位是固定的
  result.protocol.isValid = true  // 数据字节本身没有固定格式要求

  // 协议尾 (字节17)
  result.protocol.endByte = data[17]
  if (result.protocol.endByte !== 0x0e) {
    result.parseStatus.warnings.push(`协议尾错误: 期望0x0E, 实际0x${result.protocol.endByte.toString(16).toUpperCase()}`)
  }

  // 🔧 校验和5验证 (字节5: 对字节1-4, 6-11进行异或)
  result.protocol.checksum5.actual = data[5]
  let calculated5 = 0
  const bytesToCheck5 = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11]
  for (const byteIndex of bytesToCheck5) {
    calculated5 ^= data[byteIndex]
  }
  calculated5 &= 0xFF

  result.protocol.checksum5.calculated = calculated5
  result.protocol.checksum5.isValid = calculated5 === result.protocol.checksum5.actual

  if (!result.protocol.checksum5.isValid) {
    result.parseStatus.warnings.push(`校验和5错误: 期望0x${calculated5.toString(16).toUpperCase()}, 实际0x${result.protocol.checksum5.actual.toString(16).toUpperCase()}`)
  }

  // 🔧 校验和16验证 (字节16: 对字节1-4, 6-15进行异或)
  result.protocol.checksum16.actual = data[16]
  let calculated16 = 0
  const bytesToCheck16 = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
  for (const byteIndex of bytesToCheck16) {
    calculated16 ^= data[byteIndex]
  }
  calculated16 &= 0xFF

  result.protocol.checksum16.calculated = calculated16
  result.protocol.checksum16.isValid = calculated16 === result.protocol.checksum16.actual

  if (!result.protocol.checksum16.isValid) {
    result.parseStatus.warnings.push(`校验和16错误: 期望0x${calculated16.toString(16).toUpperCase()}, 实际0x${result.protocol.checksum16.actual.toString(16).toUpperCase()}`)
  }
}

/**
 * 解析设置数据
 */
function parseSettingsData(data: number[], result: ParsedBluetoothData) {
  // 字节0: P5参数
  result.settings.p5 = data[0]

  // 字节1: 档位和灯光状态
  const gearAndLightByte = data[1]
  const lightOn = (gearAndLightByte & 0x80) !== 0
  const gearValue = gearAndLightByte & 0x7F

  // 🔧 修复档位映射 (根据 bike.const.ts 中的 GearDirection)
  const gearMap: { [key: number]: number } = {
    0x00: 0,  // positionZero = 0x00
    0x01: 1,  // positionOne = 0x01
    0x02: 2,  // positionTwo = 0x02
    0x03: 3,  // positionThree = 0x03
    0x04: 4,  // positionFour = 0x04
    0x05: 5,  // positionFive = 0x05  ← 修复：0x05对应5档
    0x08: 5   // positionDefault = 0x08 (默认5档)
  }

  // 🔧 根据档位编码推断档位
  let gear = gearMap[gearValue] !== undefined ? gearMap[gearValue] : 5 // 默认5档
  
  result.settings.gearAndLight = {
    gear,
    gearName: `${gear}档`,
    lightOn
  }

  // 字节2: 速度和轮径
  const speedDimensionByte = data[2]
  const dimensionCode = speedDimensionByte & 0x07
  const speedBits = (speedDimensionByte >> 3) & 0x1F

  // 根据字节4判断速度范围
  const byte4 = data[4]
  const speedLimit = (byte4 & 0x20) !== 0 ? 32 : 0
  const actualSpeed = speedBits + speedLimit + 10 // 加上基础偏移10

  // 🔧 轮径处理：根据扩展位计算实际编码值
  const dimensionExpand = (byte4 & 0x80) !== 0
  const actualDimensionCode = dimensionExpand ? dimensionCode + 10 : dimensionCode
  const dimensionName = DimensionMap[actualDimensionCode] || `未知轮径(${actualDimensionCode})`

  result.settings.speedAndDimension = {
    speed: actualSpeed,
    dimensionCode: actualDimensionCode,
    dimensionName: dimensionName,
    dimensionMode: dimensionExpand ? 1 : 0
  }

  // 字节3: P1参数
  result.settings.p1 = data[3]

  // 字节4: 组合参数 (P2+P3+P4+限速+轮径扩展)
  const p2 = byte4 & 0x07
  const p3 = (byte4 >> 3) & 0x01
  const p4 = (byte4 >> 4) & 0x01
  const speedLimitFlag = (byte4 & 0x20) !== 0
  const dimensionExpandFlag = (byte4 & 0x80) !== 0
  
  result.settings.combinedParams = {
    p2,
    p3,
    p4,
    speedLimit: speedLimitFlag ? 32 : 0,
    dimensionExpand: dimensionExpandFlag
  }

  // 字节6: C1C2参数
  const c1c2Byte = data[6]
  result.settings.c1c2 = {
    c1: (c1c2Byte >> 3) & 0x1F,
    c2: c1c2Byte & 0x07
  }

  // 字节7: C5C14参数
  // 根据 useSetting.ts: writeData.value[7] = (128 + c14Hex + c5Hex) & 0xFF
  // 其中 c14Hex = Int2Bytes(c14.value) << 5
  const c5c14Byte = data[7]

  // 🔧 修复C14解析：需要减去128基础值，然后正确提取C14和C5
  const adjustedByte = c5c14Byte - 128  // 减去基础值128
  const c5 = adjustedByte & 0x1F        // 低5位是C5
  const c14 = (adjustedByte >> 5) & 0x07 // 高3位是C14

  result.settings.c5c14 = {
    c5: c5,
    c14: c14
  }

  // 字节8: C4C7C12参数
  const c4c7c12Byte = data[8]
  result.settings.c4c7c12 = {
    c4: (c4c7c12Byte >> 5) & 0x07,
    c7: (c4c7c12Byte >> 3) & 0x03,
    c12: c4c7c12Byte & 0x07
  }

  // 字节9: 把手最大速度
  result.settings.handlebarMaxSpeed = data[9]

  // 字节10: C13参数
  const c13Byte = data[10]
  result.settings.c13 = (c13Byte >> 2) & 0x3F

  // 字节11: 百分比
  result.settings.percent = data[11]
}

/**
 * 解析导航数据
 */
function parseNavigationData(data: number[], result: ParsedBluetoothData) {
  // 字节12: 镜像位 + 单次距离规则 + 方向
  const byte12 = data[12]
  const mirrorEnabled = (byte12 & 0x80) !== 0
  const singleDistanceRule = (byte12 >> 4) & 0x03
  const direction = byte12 & 0x0F

  result.navigation.mirror.enabled = mirrorEnabled
  result.navigation.direction = {
    code: direction,
    name: Directions[direction as keyof typeof Directions] || `未知方向(${direction})`
  }

  // 字节13: 单次距离低位
  const singleLow = data[13]

  // 字节14: 单次距离高位 + 总距离规则 + 总距离高位
  const byte14 = data[14]
  const singleHigh = (byte14 >> 6) & 0x03      // 位7-6: 单次距离高位
  const totalDistanceRule = (byte14 >> 4) & 0x03  // 位5-4: 总距离规则
  const totalHigh = (byte14 >> 2) & 0x03       // 🔧 修复: 位3-2: 总距离高位

  // 字节15: 总距离低位
  const totalLow = data[15]

  // 计算实际距离
  const singleEffectiveNumber = (singleHigh << 8) | singleLow
  const totalEffectiveNumber = (totalHigh << 8) | totalLow

  // 🔧 修复距离规则理解：规则表示编码倍数
  // 规则0=个位显示(存储原值), 规则1=十位显示(存储值×10), 规则2=百位显示(存储值×100), 规则3=千位显示(存储值×1000)
  const ruleNames = ['个位', '十位', '百位', '千位']
  const ruleMultipliers = [1, 10, 100, 1000]  // 解码倍数

  // 🔧 修复：实际距离 = 有效数字 × 规则倍数
  result.navigation.singleDistance = {
    rule: singleDistanceRule,
    ruleName: ruleNames[singleDistanceRule] || '未知',
    high: singleHigh,
    low: singleLow,
    effectiveNumber: singleEffectiveNumber,
    actualDistance: singleEffectiveNumber * ruleMultipliers[singleDistanceRule]
  }

  result.navigation.totalDistance = {
    rule: totalDistanceRule,
    ruleName: ruleNames[totalDistanceRule] || '未知',
    high: totalHigh,
    low: totalLow,
    effectiveNumber: totalEffectiveNumber,
    actualDistance: totalEffectiveNumber * ruleMultipliers[totalDistanceRule]
  }
}

/**
 * 格式化解析结果为可读字符串
 */
export function formatParsedData(parsed: ParsedBluetoothData): string {
  let output = '# 蓝牙数据解析结果\n\n'
  
  // 原始数据
  output += `## 原始数据\n`
  output += `${parsed.rawData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ')}\n\n`
  
  // 协议信息
  output += `## 协议信息\n`
  output += `- 数据字节(0-11): ${parsed.protocol.dataBytes.map(b => `0x${b.toString(16).toUpperCase()}`).join(' ')} ${parsed.protocol.isValid ? '✅' : '❌'}\n`
  output += `- 协议尾: 0x${parsed.protocol.endByte.toString(16).toUpperCase()} ${parsed.protocol.endByte === 0x0e ? '✅' : '❌'}\n`
  output += `- 校验和5: 0x${parsed.protocol.checksum5.actual.toString(16).toUpperCase()} (期望: 0x${parsed.protocol.checksum5.calculated.toString(16).toUpperCase()}) ${parsed.protocol.checksum5.isValid ? '✅' : '❌'}\n`
  output += `- 校验和16: 0x${parsed.protocol.checksum16.actual.toString(16).toUpperCase()} (期望: 0x${parsed.protocol.checksum16.calculated.toString(16).toUpperCase()}) ${parsed.protocol.checksum16.isValid ? '✅' : '❌'}\n\n`
  
  // 设置数据
  output += `## 设置数据\n`
  output += `- P5参数: ${parsed.settings.p5}\n`
  output += `- 档位: ${parsed.settings.gearAndLight.gearName}, 灯光: ${parsed.settings.gearAndLight.lightOn ? '开启' : '关闭'}\n`
  output += `- 限速: ${parsed.settings.speedAndDimension.speed}km/h, 轮径: ${parsed.settings.speedAndDimension.dimensionName}\n`
  output += `- P1: ${parsed.settings.p1}, P2: ${parsed.settings.combinedParams.p2}, P3: ${parsed.settings.combinedParams.p3}, P4: ${parsed.settings.combinedParams.p4}\n`
  output += `- C1: ${parsed.settings.c1c2.c1}, C2: ${parsed.settings.c1c2.c2}\n`
  output += `- C5: ${parsed.settings.c5c14.c5}, C14: ${parsed.settings.c5c14.c14}\n`
  output += `- C4: ${parsed.settings.c4c7c12.c4}, C7: ${parsed.settings.c4c7c12.c7}, C12: ${parsed.settings.c4c7c12.c12}\n`
  output += `- 把手最大速度: ${parsed.settings.handlebarMaxSpeed}km/h\n`
  output += `- C13: ${parsed.settings.c13}, 百分比: ${parsed.settings.percent}%\n\n`
  
  // 导航数据
  output += `## 导航数据\n`
  output += `- 方向: ${parsed.navigation.direction.name} (代码: ${parsed.navigation.direction.code})\n`
  output += `- 镜像: ${parsed.navigation.mirror.enabled ? '开启' : '关闭'}\n`
  output += `- 单次距离: ${parsed.navigation.singleDistance.actualDistance}m (${parsed.navigation.singleDistance.ruleName}显示, 有效数字: ${parsed.navigation.singleDistance.effectiveNumber})\n`
  output += `- 总距离: ${parsed.navigation.totalDistance.actualDistance}m (${parsed.navigation.totalDistance.ruleName}显示, 有效数字: ${parsed.navigation.totalDistance.effectiveNumber})\n\n`
  
  // 解析状态
  if (parsed.parseStatus.errors.length > 0) {
    output += `## 错误\n`
    parsed.parseStatus.errors.forEach(error => {
      output += `- ❌ ${error}\n`
    })
    output += '\n'
  }
  
  if (parsed.parseStatus.warnings.length > 0) {
    output += `## 警告\n`
    parsed.parseStatus.warnings.forEach(warning => {
      output += `- ⚠️ ${warning}\n`
    })
    output += '\n'
  }
  
  return output
}
