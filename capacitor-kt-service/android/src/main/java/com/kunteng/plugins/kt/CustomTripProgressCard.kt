package com.kunteng.plugins.kt

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import com.google.android.material.button.MaterialButton
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.kunteng.plugins.kt.R

class CustomTripProgressCard @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "CustomTripProgressCard"
    }

    private lateinit var bottomSheetBehavior: BottomSheetBehavior<CustomTripProgressCard>

    // Views
    private lateinit var dragIndicator: View
    private lateinit var summaryContainer: LinearLayout
    private lateinit var detailsContainer: LinearLayout

    // Summary views
    private lateinit var timeValue: TextView
    private lateinit var distanceValue: TextView
    private lateinit var etaValue: TextView
    private lateinit var controlButton: com.google.android.material.button.MaterialButton

    // Detail views
    private lateinit var altitudeValue: TextView
    private lateinit var speedValue: TextView
    private lateinit var maxSpeedValue: TextView
    private lateinit var avgSpeedValue: TextView
    private lateinit var castToggleButton: MaterialButton
    private lateinit var minimizeButton: MaterialButton
    private lateinit var endButton: MaterialButton

    // Listeners
    var onCastToggleClickListener: (() -> Unit)? = null
    var onMinimizeClickListener: (() -> Unit)? = null
    var onEndClickListener: (() -> Unit)? = null
    var onControlClickListener: ((isPaused: Boolean) -> Unit)? = null

    private var isPaused = false
    private var isCastConnected = false

    init {
        initView()
    }

    private fun initView() {
        LayoutInflater.from(context).inflate(R.layout.custom_trip_progress_card, this, true)

        // Initialize views
        dragIndicator = findViewById(R.id.drag_indicator)
        summaryContainer = findViewById(R.id.summary_container)
        detailsContainer = findViewById(R.id.details_container)
        // 保持 root/summary 不拦截触摸，交给 BottomSheetBehavior 处理拖拽
        setOnTouchListener { _, _ -> false }
        summaryContainer.setOnTouchListener { _, _ -> false }

        // Summary views
        timeValue = findViewById(R.id.time_value)
        distanceValue = findViewById(R.id.distance_value)
        etaValue = findViewById(R.id.eta_value)
        controlButton = findViewById(R.id.control_button)

        // Detail views
        altitudeValue = findViewById(R.id.altitude_value)
        speedValue = findViewById(R.id.speed_value)
        maxSpeedValue = findViewById(R.id.max_speed_value)
        avgSpeedValue = findViewById(R.id.avg_speed_value)
        castToggleButton = findViewById(R.id.cast_toggle_button)
        minimizeButton = findViewById(R.id.nav_minimize_button)
        endButton = findViewById(R.id.end_button)

        setupBottomSheet()
        setupClickListeners()
    }

    private fun setupClickListeners() {
        // 顶部指示条点击切换；简介区域不消费触摸，保证可拖拽
        summaryContainer.isClickable = false
        dragIndicator.setOnClickListener { toggleExpanded() }

        // Control buttons
        controlButton.setOnClickListener {
            togglePlayPause()
        }

        // Bottom buttons
        castToggleButton.setOnClickListener { onCastToggleClickListener?.invoke() }
        minimizeButton.setOnClickListener { onMinimizeClickListener?.invoke() }
        endButton.setOnClickListener { onEndClickListener?.invoke() }
    }

    private fun togglePlayPause() {
        isPaused = !isPaused
        updateControlButtons()
        onControlClickListener?.invoke(isPaused)
    }

    private fun updateControlButtons() {
        val iconRes = if (isPaused) R.drawable.ic_play else R.drawable.ic_pause
        controlButton.setIconResource(iconRes)
    }

    private fun toggleExpanded() {
        if (isCardExpanded()) collapseCard() else expandCard()
    }

    private fun setupBottomSheet() {
        // 在附着到窗口之后再初始化，以确保已在 CoordinatorLayout 中
        post {
            bottomSheetBehavior = BottomSheetBehavior.from(this)
            bottomSheetBehavior.isHideable = false
            bottomSheetBehavior.skipCollapsed = false
            bottomSheetBehavior.setFitToContents(true)
            bottomSheetBehavior.isDraggable = true
            // 避免手势导航栏产生的底部 inset 影响
            bottomSheetBehavior.isGestureInsetBottomIgnored = true

            // 初始状态为折叠
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
            // 初始化：简介可见，详情保持在布局中但透明，避免高度跳变
            summaryContainer.visibility = View.VISIBLE
            detailsContainer.visibility = View.VISIBLE
            detailsContainer.alpha = 0f
            detailsContainer.isClickable = false

            // 收起高度固定为 136dp（设计中的 pt 近似等同于 Android 的 dp）
            val collapsedHeightPx = dpToPx(136)
            bottomSheetBehavior.peekHeight = collapsedHeightPx

            bottomSheetBehavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    when (newState) {
                        BottomSheetBehavior.STATE_EXPANDED -> {
                            // 展开：简介 + 详情
                            summaryContainer.visibility = View.VISIBLE
                            summaryContainer.alpha = 1f
                            detailsContainer.visibility = View.VISIBLE
                            detailsContainer.alpha = 1f
                            detailsContainer.isClickable = true
                        }
                        BottomSheetBehavior.STATE_COLLAPSED -> {
                            // 收起：仅显示简介；详情保持在布局中但透明
                            summaryContainer.visibility = View.VISIBLE
                            summaryContainer.alpha = 1f
                            detailsContainer.visibility = View.VISIBLE
                            detailsContainer.alpha = 0f
                            detailsContainer.isClickable = false
                        }
                        else -> { /* no-op */ }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    val offset = slideOffset.coerceIn(0f, 1f)
                    summaryContainer.visibility = View.VISIBLE
                    summaryContainer.alpha = 1f
                    detailsContainer.visibility = View.VISIBLE
                    detailsContainer.alpha = offset
                }
            })
        }
    }

    private fun dpToPx(dp: Int): Int {
        val density = resources.displayMetrics.density
        return (dp * density).toInt()
    }

    // Public methods to update data
    fun updateTime(time: String) {
        timeValue.text = time
    }

    fun updateDistance(distance: String) {
        distanceValue.text = distance
    }

    fun updateEta(eta: String) {
        etaValue.text = eta
    }

    fun updateAltitude(altitude: String) {
        altitudeValue.text = altitude
    }

    fun updateSpeed(speed: String) {
        speedValue.text = speed
    }

    fun updateMaxSpeed(maxSpeed: String) {
        maxSpeedValue.text = maxSpeed
    }

    fun updateAvgSpeed(avgSpeed: String) {
        avgSpeedValue.text = avgSpeed
    }

    fun setControlState(paused: Boolean) {
        isPaused = paused
        updateControlButtons()
    }

    fun isCardExpanded(): Boolean =
        ::bottomSheetBehavior.isInitialized && bottomSheetBehavior.state == BottomSheetBehavior.STATE_EXPANDED

    fun expandCard() {
        if (::bottomSheetBehavior.isInitialized) {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    fun collapseCard() {
        if (::bottomSheetBehavior.isInitialized) {
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
        }
    }

    // 这里不再需要手势监听器，交由 BottomSheetBehavior 处理
}
