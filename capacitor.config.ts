import { CapacitorConfig } from '@capacitor/cli'
import { KeyboardResize, KeyboardStyle } from '@capacitor/keyboard'

const config: CapacitorConfig = {
  appId: 'com.dongsipan.smartbicycle',
  appName: 'KT Smart Bicycle',
  webDir: 'dist',
  includePlugins: [
    '@capacitor-community/bluetooth-le',
    '@capacitor-community/keep-awake',
    '@capacitor/app',
    '@capacitor/haptics',
    '@capacitor/keyboard',
    '@capacitor/screen-orientation',
    '@capacitor/status-bar',
    'capacitor-kt-service'
  ],
  android: {
    useLegacyBridge: true
  },
  server: {
    androidScheme: 'https'
  },
  plugins: {
    Keyboard: {
      resize: KeyboardResize.Body,
      style: KeyboardStyle.Dark,
      resizeOnFullScreen: true
    },
    StatusBar: {
      overlaysWebView: false,
      style: 'light',
      backgroundColor: '#000000'
    },
    BluetoothLe: {
      displayStrings: {
        scanning: "正在扫描蓝牙设备...",
        cancel: "取消",
        availableDevices: "可用设备",
        noDeviceFound: "未找到设备"
      }
    },
    BackgroundService: {
      // 自定义插件配置
    },
    KeepAwake: {
      // 保持屏幕唤醒配置
    }
  }
}

export default config
