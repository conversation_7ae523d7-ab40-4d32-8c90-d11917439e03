<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/place_card_background">

    <!-- 地点名称 -->
    <TextView
        android:id="@+id/place_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:text="Green Treasure Plaza"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:textStyle="bold"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintEnd_toStartOf="@+id/distance_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 距离 -->
    <TextView
        android:id="@+id/distance_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:text="34.8 km"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 地址 -->
    <TextView
        android:id="@+id/place_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text="436 Changjiang Road, Huqiu District, Suzhou City, Jiangsu Province, China"
        android:textColor="#CCFFFFFF"
        android:textSize="16sp"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/place_name" />

    <!-- Navigate 按钮 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/go_here_button"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/go_here_button_background"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/place_address">

        <!-- Navigate 文字 - 居中显示 -->
        <TextView
            android:id="@+id/go_here_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Navigate"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 导航图标 - 左侧显示 -->
        <ImageView
            android:id="@+id/navigation_icon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_right"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/go_here_text"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
