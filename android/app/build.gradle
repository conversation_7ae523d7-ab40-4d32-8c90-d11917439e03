apply plugin: 'com.android.application'

android {
    namespace "com.dongsipan.smartbicycle"
    compileSdk rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "com.dongsipan.smartbicycle"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "2.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    signingConfigs {
      release { 
        keyAlias 'release' 
        keyPassword '518619' 
        storeFile file('../../android-release.keystore')
        storePassword '518619' 
      } 
      debug { 
        keyAlias 'debug' 
        keyPassword '518619' 
        storeFile file('../../android-debug.keystore')
        storePassword '518619' 
      }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        dataBinding true
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}

// Convenience task: build+install then relaunch the debug app on device
tasks.register("runDebug") {
    dependsOn("installDebug")
    doLast {
        exec {
            commandLine "adb", "shell", "am", "force-stop", "com.dongsipan.smartbicycle"
        }
        exec {
            commandLine "adb", "shell", "monkey", "-p", "com.dongsipan.smartbicycle", "-c", "android.intent.category.LAUNCHER", "1"
        }
    }
}
