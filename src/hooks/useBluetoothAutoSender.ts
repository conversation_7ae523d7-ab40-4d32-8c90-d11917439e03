import { watch, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useBleStore } from '@/store/useBleStore';
// import { useSmartBluetoothMessage } from './useSmartBluetoothMessage'; // 简化后不再需要
import { useBluetoothManager } from '@/composables/useBluetoothManager';
import { createSingleton } from '@/utils/bluetoothSingleton';

/**
 * 蓝牙自动发送Hook - 响应式架构
 * 全局监听蓝牙连接状态，自动启动/停止数据发送
 *
 * 功能：
 * 1. 监听蓝牙连接状态变化
 * 2. 连接成功时自动启动智能发送
 * 3. 断开连接时自动停止发送
 * 4. 根据平台自动选择最佳发送方案
 */
export function useBluetoothAutoSender() {
  return createSingleton('useBluetoothAutoSender', () => {
    console.log("🔧 useBluetoothAutoSender - 使用响应式架构");
  const { connectedDevice } = storeToRefs(useBleStore());

  // 🔧 使用新的响应式架构
  const bluetoothManager = useBluetoothManager();
  const { startSending, stopSending, isSending, updateAndSend } = bluetoothManager;

  // 移除未使用的智能蓝牙hook，简化代码

  // 监听器状态
  const isListenerActive = ref(false);
  const lastConnectionState = ref(false);

  /**
   * 等待蓝牙初始化完成并启动发送服务
   */
  const waitForBluetoothInitAndStart = async () => {
    const maxWaitTime = 15000; // 最大等待15秒
    const checkInterval = 500; // 每500ms检查一次
    const startTime = Date.now();

    const checkAndStart = async () => {
      try {
        // 检查是否超时
        if (Date.now() - startTime > maxWaitTime) {
          console.error("❌ 等待蓝牙初始化超时");
          return;
        }

        // 检查连接状态是否还有效
        if (!connectedDevice.value?.isPaired) {
          console.warn("⚠️ 设备连接状态已变化，取消启动");
          return;
        }

        // 尝试初始化蓝牙
        const { useBluetoothLe } = await import("@/hooks/useBluetooth-le");
        const { initialBle } = useBluetoothLe();

        console.log("🔧 尝试初始化蓝牙LE...");
        await initialBle();
        console.log("✅ 蓝牙LE初始化成功，启动发送服务");

        // 启动发送服务
        if (isSending.value) {
          console.log("✅ 发送服务已运行，更新数据");
          await updateAndSend();
        } else {
          await startSending();
          console.log("✅ 蓝牙发送服务启动成功");
        }

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.warn("⚠️ 蓝牙初始化未完成，继续等待...", errorMsg);

        // 继续等待
        setTimeout(checkAndStart, checkInterval);
      }
    };

    // 开始检查
    checkAndStart();
  };

  /**
   * 启动蓝牙连接监听器
   */
  const startBluetoothConnectionListener = () => {
    if (isListenerActive.value) {
      console.log('🔄 蓝牙连接监听器已经在运行');
      return;
    }

    console.log('🎯 启动全局蓝牙连接监听器');

    // 初始化状态
    lastConnectionState.value = connectedDevice.value?.isPaired || false;
    isListenerActive.value = true;

    // 🔧 检查启动时是否已经连接，如果是则等待初始化完成后处理
    if (connectedDevice.value?.isPaired) {
      console.log('🔍 启动时检测到设备已连接，等待蓝牙初始化完成后启动发送服务');
      waitForBluetoothInitAndStart();
    }

    // 监听蓝牙连接状态变化
    const stopWatcher = watch(
      () => connectedDevice.value?.isPaired || false,
      async (isPaired, wasPaired) => {
        console.log(`🔄 蓝牙连接状态变化: ${wasPaired} -> ${isPaired}`);

        if (isPaired && !wasPaired) {
          // 蓝牙刚刚连接成功
          await handleBluetoothConnected();
        } else if (!isPaired && wasPaired) {
          // 蓝牙断开连接
          await handleBluetoothDisconnected();
        }

        lastConnectionState.value = isPaired;
      },
      { immediate: false } // 不立即执行，只监听变化
    );

    // 返回停止监听的方法
    return stopWatcher;
  };

  // 移除 handleBluetoothConnectedWithInit，使用 waitForBluetoothInitAndStart 替代

  /**
   * 处理蓝牙连接成功 - 用于运行时连接变化
   */
  const handleBluetoothConnected = async () => {
    console.log("🎉 蓝牙连接成功，准备启动发送服务");

    // 延迟1.5秒确保连接稳定
    setTimeout(async () => {
      try {
        // 再次确认连接状态
        if (!connectedDevice.value?.isPaired) {
          console.warn("⚠️ 连接状态已变化，取消启动");
          return;
        }

        console.log("🚀 启动蓝牙发送服务");

        // 如果已经在发送，更新数据
        if (isSending.value) {
          console.log("✅ 发送服务已运行，更新数据");
          await updateAndSend();
          return;
        }

        // 启动发送服务
        await startSending();
        console.log("✅ 蓝牙发送服务启动成功");

      } catch (error) {
        console.error("❌ 启动发送服务失败:", error);
      }
    }, 1500);
  };

  /**
   * 处理蓝牙断开连接 - 使用响应式架构
   */
  const handleBluetoothDisconnected = async () => {
    console.log("📱 蓝牙连接断开，停止发送服务");

    try {
      if (isSending.value) {
        await stopSending();
        console.log("✅ 发送服务已停止");
      }
    } catch (error) {
      console.error("❌ 停止发送服务失败:", error);
    }
  };

  /**
   * 手动检查并恢复发送状态 - 使用响应式架构
   * 用于页面进入时确保服务正常运行
   */
  const checkAndRestoreBluetoothSending = async () => {
    if (!connectedDevice.value?.isPaired) {
      console.log("🔍 蓝牙未连接，无需检查");
      return;
    }

    console.log("🔍 检查蓝牙发送状态");

    const maxRetries = 5; // 最多重试5次
    let retryCount = 0;

    const tryInitAndStart = async (): Promise<boolean> => {
      try {
        // 确保蓝牙LE已初始化
        const { useBluetoothLe } = await import("@/hooks/useBluetooth-le");
        const { initialBle } = useBluetoothLe();

        console.log("🔧 确保蓝牙LE已初始化...");
        await initialBle();
        console.log("✅ 蓝牙LE初始化确认完成");

        if (!isSending.value) {
          console.log("🚀 发送服务未运行，启动服务");
          await startSending();
          console.log("✅ 发送服务已启动");
        } else {
          console.log("✅ 发送服务正常运行");
          await updateAndSend();
        }
        return true;
      } catch (error) {
        retryCount++;
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.warn(`⚠️ 蓝牙初始化失败 (${retryCount}/${maxRetries}):`, errorMsg);

        if (retryCount < maxRetries) {
          console.log(`🔄 等待1秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return await tryInitAndStart();
        } else {
          console.error("❌ 蓝牙初始化重试次数已达上限");
          return false;
        }
      }
    };

    await tryInitAndStart();
  };

  /**
   * 🔧 强制更新蓝牙发送数据 - 使用响应式架构
   * 用于设置更新后立即同步数据
   */
  const forceUpdateBluetoothData = async () => {
    console.log("🔧 强制更新蓝牙发送数据（响应式）");

    try {
      // 🔧 使用响应式架构的统一更新方法
      await updateAndSend();
      console.log("✅ 蓝牙发送数据强制更新完成");
    } catch (error) {
      console.error("❌ 强制更新蓝牙发送数据失败:", error);
    }
  };

  /**
   * 停止蓝牙连接监听器
   */
  const stopBluetoothConnectionListener = () => {
    if (isListenerActive.value) {
      isListenerActive.value = false;
      console.log('🛑 蓝牙连接监听器已停止');
    }
  };

  /**
   * 获取当前状态信息
   */
  const getBluetoothAutoSenderStatus = () => {
    return {
      isListenerActive: isListenerActive.value,
      isBluetoothConnected: connectedDevice.value?.isPaired || false,
      isSending: isSending.value,
      currentMethod: bluetoothManager.currentMethod.value,
      lastConnectionState: lastConnectionState.value,
      timestamp: Date.now()
    };
  };

  const instance = {
    // 主要控制方法
    startBluetoothConnectionListener,
    stopBluetoothConnectionListener,
    checkAndRestoreBluetoothSending,
    forceUpdateBluetoothData, // 🔧 新增：强制更新方法

    // 状态信息
    isListenerActive,
    getBluetoothAutoSenderStatus,

    // 🔧 响应式架构的状态访问
    get isServiceRunning() {
      return isSending;
    },
    get currentMethod() {
      return bluetoothManager.currentMethod || ref('responsive');
    },

    // 调试信息
    debugInfo: () => ({
      hookName: 'useBluetoothAutoSender',
      description: '全局蓝牙自动发送管理器',
      features: [
        '监听蓝牙连接状态变化',
        '自动启动/停止数据发送',
        '智能平台方案选择',
        '连接状态恢复检查'
      ]
    })
  };

  return instance;
  }); // 单例工厂函数结束
}
