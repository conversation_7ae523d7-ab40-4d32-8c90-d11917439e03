package com.kunteng.plugins.kt;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.os.PowerManager;
import android.content.Context;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.os.Bundle;
import android.util.Log;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.getcapacitor.JSObject;
import com.getcapacitor.JSArray;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.Permission;
import com.getcapacitor.annotation.PermissionCallback;
import androidx.activity.result.ActivityResult;
import android.app.Activity;
import com.kunteng.plugins.kt.NavigationDialogFragment;
import com.kunteng.plugins.kt.NavigationManager;
import org.json.JSONObject;

// MapBox Search imports
import java.util.List;

/**
 * KunTeng Electronics Native Services
 * 昆腾电子原生服务 - 集成蓝牙后台通信、Mapbox导航和搜索功能
 */
@CapacitorPlugin(
    name = "CapacitorKtService",
    permissions = {
        @Permission(
            alias = "location",
            strings = {
                android.Manifest.permission.ACCESS_FINE_LOCATION,
                android.Manifest.permission.ACCESS_COARSE_LOCATION
            }
        )
    }
)
public class CapacitorKtService extends Plugin {

    private static final String TAG = "CapacitorKtService";

    // 静态实例，供其他类访问
    public static CapacitorKtService instance;

    // 蓝牙后台服务相关
    private BluetoothForegroundService bluetoothServiceInstance;
    
    // 存储最新接收到的蓝牙数据，用于导航数据修改的基础
    private static int[] latestReceivedBluetoothData = null;
    private static final Object bluetoothDataLock = new Object();
    
    // Mapbox导航相关
    private CapacitorMapboxNavigation mapboxNavigationImpl = new CapacitorMapboxNavigation();
    private PluginCall currentNavigationCall;
    


    public static CapacitorKtService getInstance() {
        return instance;
    }

    // 获取当前导航调用
    public PluginCall getCurrentNavigationCall() {
        return currentNavigationCall;
    }

    // 设置当前导航调用
    public void setCurrentNavigationCall(PluginCall call) {
        this.currentNavigationCall = call;
    }



    @Override
    public void load() {
        super.load();
        instance = this;
    }

    // =================== 通用功能 ===================

    // =================== 蓝牙后台通信功能 ===================

    @PluginMethod
    public void startBluetoothForegroundService(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "START");
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getContext().startForegroundService(serviceIntent);
            } else {
                getContext().startService(serviceIntent);
            }
            
            // 通知状态变化
            notifyBluetoothServiceStateChanged(true);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to start bluetooth foreground service: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void stopBluetoothForegroundService(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "STOP");
            getContext().stopService(serviceIntent);
            
            // 通知状态变化
            notifyBluetoothServiceStateChanged(false);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to stop bluetooth foreground service: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void requestIgnoreBatteryOptimizations(PluginCall call) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager pm = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
                String packageName = getContext().getPackageName();
                
                if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                    intent.setData(Uri.parse("package:" + packageName));
                    startActivityForResult(call, intent, "batteryOptimizationResult");
                    return;
                }
            }
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to request battery optimization: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void isBluetoothServiceRunning(PluginCall call) {
        try {
            boolean isRunning = BluetoothForegroundService.isServiceRunning();
            JSObject ret = new JSObject();
            ret.put("isRunning", isRunning);
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to check service status: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void updateBluetoothNotification(PluginCall call) {
        try {
            String title = call.getString("title", "蓝牙后台服务");
            String message = call.getString("message", "正在运行中...");
            
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "UPDATE_NOTIFICATION");
            serviceIntent.putExtra("title", title);
            serviceIntent.putExtra("message", message);
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to update notification: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void checkDozeMode(PluginCall call) {
        try {
            JSObject ret = new JSObject();
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager pm = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
                String packageName = getContext().getPackageName();
                
                boolean isInDozeMode = pm.isDeviceIdleMode();
                boolean isIgnoringBatteryOptimizations = pm.isIgnoringBatteryOptimizations(packageName);
                
                ret.put("isInDozeMode", isInDozeMode);
                ret.put("isIgnoringBatteryOptimizations", isIgnoringBatteryOptimizations);
            } else {
                ret.put("isInDozeMode", false);
                ret.put("isIgnoringBatteryOptimizations", true);
            }
            
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to check doze mode: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void openBatteryOptimizationSettings(PluginCall call) {
        try {
            Intent intent = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
            getActivity().startActivity(intent);
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to open battery optimization settings: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void sendKeepAlive(PluginCall call) {
        try {
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "KEEP_ALIVE");
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to send keep alive: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getDeviceInfo(PluginCall call) {
        try {
            JSObject ret = new JSObject();
            ret.put("manufacturer", Build.MANUFACTURER);
            ret.put("model", Build.MODEL);
            ret.put("androidVersion", Build.VERSION.RELEASE);
            ret.put("sdkVersion", Build.VERSION.SDK_INT);
            
            // 检查是否有激进的电源管理
            boolean hasAggressivePowerManagement = isAggressivePowerManagementDevice();
            ret.put("hasAggressivePowerManagement", hasAggressivePowerManagement);
            
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get device info: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void openAutoStartSettings(PluginCall call) {
        try {
            openAutoStartSettingsForManufacturer();
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to open auto start settings: " + e.getMessage(), e);
        }
    }

    // 蓝牙发送相关方法
    @PluginMethod
    public void startNativeBluetoothSending(PluginCall call) {
        try {
            String deviceId = call.getString("deviceId");
            String serviceUUID = call.getString("serviceUUID");
            String characteristicUUID = call.getString("characteristicUUID");
            int sendInterval = call.getInt("sendInterval", 106);
            JSArray dataArray = call.getArray("data");
            
            if (deviceId == null || serviceUUID == null || characteristicUUID == null) {
                call.reject("Missing required parameters");
                return;
            }
            
            int[] data = new int[dataArray.length()];
            for (int i = 0; i < dataArray.length(); i++) {
                try {
                    if (dataArray.isNull(i)) {
                        Log.w(TAG, "数据数组第" + i + "位为null，使用默认值0");
                        data[i] = 0;
                    } else {
                        data[i] = dataArray.getInt(i);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "数据数组第" + i + "位转换失败，使用默认值0: " + e.getMessage());
                    data[i] = 0;
                }
            }
            
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "START_BLUETOOTH_SENDING");
            serviceIntent.putExtra("deviceId", deviceId);
            serviceIntent.putExtra("serviceUUID", serviceUUID);
            serviceIntent.putExtra("characteristicUUID", characteristicUUID);
            serviceIntent.putExtra("sendInterval", sendInterval);
            serviceIntent.putExtra("data", data);
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to start native bluetooth sending: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void stopNativeBluetoothSending(PluginCall call) {
        try {
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "STOP_BLUETOOTH_SENDING");
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to stop native bluetooth sending: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void updateBluetoothSendData(PluginCall call) {
        try {
            JSArray dataArray = call.getArray("data");
            if (dataArray == null) {
                call.reject("Missing data parameter");
                return;
            }
            
            int[] data = new int[dataArray.length()];
            for (int i = 0; i < dataArray.length(); i++) {
                try {
                    if (dataArray.isNull(i)) {
                        Log.w(TAG, "数据数组第" + i + "位为null，使用默认值0");
                        data[i] = 0;
                    } else {
                        data[i] = dataArray.getInt(i);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "数据数组第" + i + "位转换失败，使用默认值0: " + e.getMessage());
                    data[i] = 0;
                }
            }
            
            // 🔧 增强调试：记录从JS端接收到的数据
            StringBuilder dataLog = new StringBuilder("🌐 从JS端接收到数据更新: [");
            for (int i = 0; i < Math.min(data.length, 10); i++) {
                dataLog.append(data[i]);
                if (i < Math.min(data.length, 10) - 1) dataLog.append(", ");
            }
            if (data.length > 10) dataLog.append("...");
            dataLog.append("] 长度: ").append(data.length);
            
            Log.d(TAG, dataLog.toString());
            Log.d(TAG, "🕐 Plugin收到JS请求时间戳: " + System.currentTimeMillis());
            
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "UPDATE_BLUETOOTH_DATA");
            serviceIntent.putExtra("data", data);
            getContext().startService(serviceIntent);
            
            Log.d(TAG, "✅ 数据更新Intent已发送给BluetoothForegroundService");
            
            call.resolve();
        } catch (Exception e) {
            Log.e(TAG, "❌ updateBluetoothSendData失败: " + e.getMessage(), e);
            call.reject("Failed to update bluetooth send data: " + e.getMessage(), e);
        }
    }

    /**
     * 内部调用的蓝牙数据发送方法，用于Native端直接发送数据
     * @param data 要发送的数据数组
     */
    public void updateBluetoothSendDataInternal(int[] data) {
        try {
            if (data == null) {
                Log.w(TAG, "⚠️ updateBluetoothSendDataInternal收到null数据，忽略");
                return;
            }
            
            // 🔧 增强调试：记录从Native端接收到的数据
            StringBuilder dataLog = new StringBuilder("🔧 从Native端接收到数据更新: [");
            for (int i = 0; i < Math.min(data.length, 10); i++) {
                dataLog.append(data[i]);
                if (i < Math.min(data.length, 10) - 1) dataLog.append(", ");
            }
            if (data.length > 10) dataLog.append("...");
            dataLog.append("] 长度: ").append(data.length);
            
            Log.d(TAG, dataLog.toString());
            Log.d(TAG, "🕐 Internal方法收到请求时间戳: " + System.currentTimeMillis());
            
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "UPDATE_BLUETOOTH_DATA");
            serviceIntent.putExtra("data", data);
            getContext().startService(serviceIntent);
            
            Log.d(TAG, "✅ 内部数据更新Intent已发送给BluetoothForegroundService");
        } catch (Exception e) {
            Log.e(TAG, "❌ updateBluetoothSendDataInternal失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新最新接收到的蓝牙数据
     * 用于导航数据修改时作为基础数据使用
     * @param data 接收到的蓝牙数据数组
     */
    public void updateLatestReceivedBluetoothData(int[] data) {
        synchronized (bluetoothDataLock) {
            latestReceivedBluetoothData = data.clone();
            Log.d(TAG, "更新最新接收的蓝牙数据，长度: " +
                    (latestReceivedBluetoothData != null ? latestReceivedBluetoothData.length : 0));
        }
    }

    /**
     * 获取最新接收到的蓝牙数据
     * 用于导航数据修改时作为基础数据使用
     * @return 最新接收到的蓝牙数据数组的副本，如果没有则返回null
     */
    public int[] getLatestReceivedBluetoothData() {
        synchronized (bluetoothDataLock) {
            if (latestReceivedBluetoothData != null) {
                Log.d(TAG, "获取最新接收的蓝牙数据，长度: " + latestReceivedBluetoothData.length);
                return latestReceivedBluetoothData.clone();
            } else {
                Log.w(TAG, "没有可用的最新接收蓝牙数据");
                return null;
            }
        }
    }

    @PluginMethod
    public void isNativeBluetoothSending(PluginCall call) {
        try {
            JSObject ret = BluetoothForegroundService.getBluetoothSendingStatus();
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get bluetooth sending state: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getBluetoothSendingStats(PluginCall call) {
        try {
            JSObject ret = BluetoothForegroundService.getBluetoothSendingStats();
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get bluetooth sending stats: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getCurrentBluetoothSendData(PluginCall call) {
        try {
            JSObject ret = BluetoothForegroundService.getCurrentBluetoothSendData();
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get current bluetooth send data: " + e.getMessage(), e);
        }
    }

    /**
     * 同步方法：获取当前蓝牙发送数据
     * 用于NavigationManager直接调用，避免异步回调的复杂性
     * @return 当前蓝牙发送数据数组，如果没有则返回null
     */
    public int[] getCurrentBluetoothSendDataSync() {
        try {
            JSObject result = BluetoothForegroundService.getCurrentBluetoothSendData();
            if (result != null && result.has("success") && result.getBool("success")) {
                Object dataObj = result.get("data");
                if (dataObj instanceof JSArray) {
                    JSArray dataArray = (JSArray) dataObj;
                    if (dataArray != null && dataArray.length() > 0) {
                        int[] data = new int[dataArray.length()];
                        for (int i = 0; i < dataArray.length(); i++) {
                            data[i] = dataArray.optInt(i);
                        }
                        Log.d(TAG, "同步获取当前蓝牙发送数据成功，长度: " + data.length);
                        return data;
                    }
                }
            }
            
            // 记录详细的失败信息
            String errorMsg = "同步获取当前蓝牙发送数据失败";
            if (result != null && result.has("error")) {
                errorMsg += ": " + result.getString("error");
            } else {
                errorMsg += ": 结果无效或服务未初始化";
            }
            Log.w(TAG, errorMsg);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "同步获取当前蓝牙发送数据异常: " + e.getMessage(), e);
            return null;
        }
    }

    @PluginMethod
    public void getServiceInitializationStatus(PluginCall call) {
        try {
            JSObject ret = BluetoothForegroundService.getServiceInitializationStatus();
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get service initialization status: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getMirrorState(PluginCall call) {
        try {
            // 获取NavigationManager中的镜像状态
            boolean isMirrorEnabled = NavigationManager.getGlobalMirrorState();
            
            JSObject ret = new JSObject();
            ret.put("enabled", isMirrorEnabled);
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get mirror state: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void reconnectBluetoothDevice(PluginCall call) {
        try {
            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "RECONNECT_BLUETOOTH");
            getContext().startService(serviceIntent);

            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to reconnect bluetooth device: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void startSendingAfterConnection(PluginCall call) {
        try {
            Log.d(TAG, "🔧 启动连接后发送服务");

            // 通过Intent与服务通信
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "START_SENDING_AFTER_CONNECTION");
            getContext().startService(serviceIntent);

            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "连接后发送启动指令已发送");
            call.resolve(result);

        } catch (Exception e) {
            Log.e(TAG, "❌ 启动连接后发送失败: " + e.getMessage(), e);
            JSObject result = new JSObject();
            result.put("success", false);
            result.put("message", "启动连接后发送失败: " + e.getMessage());
            call.resolve(result);
        }
    }

    // =================== Mapbox导航功能 ===================

    @PluginMethod
    public void showMapboxNavigation(PluginCall call) {
        // 检查权限，严格按照原始插件逻辑
        if (getPermissionState("location") != com.getcapacitor.PermissionState.GRANTED) {
            requestPermissionForAlias("location", call, "navigationPermissionCallback");
        } else {
            // 保持call活跃状态，与原始插件保持一致
            call.setKeepAlive(true);
            currentNavigationCall = call;
            startNavigationFromCall(call);
        }
    }

    @PluginMethod
    public void hideNavigationFragment(PluginCall call) {
        try {
            NavigationDialogFragment currentFragment = NavigationDialogFragment.getCurrentInstance();
            if (currentFragment != null) {
                // 在主线程上执行UI操作
                getActivity().runOnUiThread(() -> {
                    currentFragment.hideNavigationInterface();
                });
                
                JSObject ret = new JSObject();
                ret.put("success", true);
                call.resolve(ret);
            } else {
                JSObject ret = new JSObject();
                ret.put("success", false);
                ret.put("error", "导航界面未初始化");
                call.resolve(ret);
            }
        } catch (Exception e) {
            JSObject ret = new JSObject();
            ret.put("success", false);
            ret.put("error", e.getMessage());
            call.resolve(ret);
        }
    }

    @PluginMethod
    public void showNavigationFragment(PluginCall call) {
        try {
            NavigationDialogFragment currentFragment = NavigationDialogFragment.getCurrentInstance();
            if (currentFragment != null) {
                // 在主线程上执行UI操作
                getActivity().runOnUiThread(() -> {
                    currentFragment.showNavigationInterface();
                });
                
                JSObject ret = new JSObject();
                ret.put("success", true);
                call.resolve(ret);
            } else {
                JSObject ret = new JSObject();
                ret.put("success", false);
                ret.put("error", "导航界面未初始化");
                call.resolve(ret);
            }
        } catch (Exception e) {
            JSObject ret = new JSObject();
            ret.put("success", false);
            ret.put("error", e.getMessage());
            call.resolve(ret);
        }
    }

    @PluginMethod
    public void getNavigationFragmentVisibility(PluginCall call) {
        try {
            NavigationDialogFragment currentFragment = NavigationDialogFragment.getCurrentInstance();
            if (currentFragment != null) {
                boolean isVisible = currentFragment.getNavigationInterfaceVisibility();
                JSObject ret = new JSObject();
                ret.put("isVisible", isVisible);
                call.resolve(ret);
            } else {
                JSObject ret = new JSObject();
                ret.put("isVisible", false);
                call.resolve(ret);
            }
        } catch (Exception e) {
            JSObject ret = new JSObject();
            ret.put("isVisible", false);
            call.resolve(ret);
        }
    }

    @PluginMethod
    public void getNavigationHistory(PluginCall call) {
        try {
            // 这里可以实现导航历史记录功能
            JSObject ret = new JSObject();
            ret.put("history", new JSArray());
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get navigation history: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void requestNavigationPermissions(PluginCall call) {
        requestPermissionForAlias("location", call, "navigationPermissionCallback");
    }

    @PluginMethod
    public void checkNavigationPermissions(PluginCall call) {
        JSObject ret = new JSObject();
        ret.put("location", getPermissionState("location"));
        call.resolve(ret);
    }

    // =================== Mapbox搜索功能 ===================

    // 创建搜索实现实例
    private CapacitorMapboxSearch mapboxSearchImplementation;

    private CapacitorMapboxSearch getMapboxSearchImplementation() {
        if (mapboxSearchImplementation == null) {
            mapboxSearchImplementation = new CapacitorMapboxSearch(getContext());
        }
        return mapboxSearchImplementation;
    }

    @PluginMethod
    public void searchMapboxPlaces(PluginCall call) {
        String query = call.getString("query");
        if (query == null || query.isEmpty()) {
            call.reject("Query parameter is required");
            return;
        }

        getMapboxSearchImplementation().searchPlaces(query, new CapacitorMapboxSearch.SearchResultsCallback() {
            @Override
            public void onResults(List<com.mapbox.search.result.SearchResult> results) {
                JSObject ret = new JSObject();
                JSObject[] searchResults = new JSObject[results.size()];
                
                for (int i = 0; i < results.size(); i++) {
                    com.mapbox.search.result.SearchResult result = results.get(i);
                    JSObject resultObj = new JSObject();
                    
                    resultObj.put("id", result.getId());
                    resultObj.put("name", result.getName());
                    resultObj.put("address", result.getAddress() != null ? result.getAddress().toString() : "");
                    
                    JSObject coordinate = new JSObject();
                    com.mapbox.geojson.Point point = result.getCoordinate();
                    coordinate.put("latitude", point.latitude());
                    coordinate.put("longitude", point.longitude());
                    resultObj.put("coordinate", coordinate);
                    
                    if (result.getCategories() != null && !result.getCategories().isEmpty()) {
                        resultObj.put("categories", result.getCategories());
                    } else {
                        resultObj.put("categories", new String[]{});
                    }
                    
                    searchResults[i] = resultObj;
                }
                
                ret.put("results", searchResults);
                call.resolve(ret);
            }

            @Override
            public void onError(String error) {
                call.reject(error);
            }
        });
    }

    @PluginMethod
    public void getMapboxPlaceSuggestions(PluginCall call) {
        String query = call.getString("query");
        if (query == null || query.isEmpty()) {
            call.reject("Query parameter is required");
            return;
        }

        getMapboxSearchImplementation().getPlaceSuggestions(query, new CapacitorMapboxSearch.SuggestionsCallback() {
            @Override
            public void onSuggestions(List<com.mapbox.search.result.SearchSuggestion> suggestions) {
                JSObject ret = new JSObject();
                JSObject[] suggestionResults = new JSObject[suggestions.size()];
                
                for (int i = 0; i < suggestions.size(); i++) {
                    com.mapbox.search.result.SearchSuggestion suggestion = suggestions.get(i);
                    JSObject suggestionObj = new JSObject();
                    
                    suggestionObj.put("name", suggestion.getName());
                    suggestionObj.put("description", suggestion.getName() != null ? suggestion.getName() : "");
                    suggestionObj.put("address", suggestion.getAddress() != null ? suggestion.getAddress().toString() : "");
                    
                    suggestionResults[i] = suggestionObj;
                }
                
                ret.put("suggestions", suggestionResults);
                call.resolve(ret);
            }

            @Override
            public void onError(String error) {
                call.reject(error);
            }
        });
    }

    @PluginMethod
    public void reverseGeocodeMapbox(PluginCall call) {
        Double latitude = call.getDouble("latitude");
        Double longitude = call.getDouble("longitude");
        
        if (latitude == null || longitude == null) {
            call.reject("Latitude and longitude parameters are required");
            return;
        }

        getMapboxSearchImplementation().reverseGeocode(latitude, longitude, new CapacitorMapboxSearch.SearchResultsCallback() {
            @Override
            public void onResults(List<com.mapbox.search.result.SearchResult> results) {
                JSObject ret = new JSObject();
                JSObject[] geocodeResults = new JSObject[results.size()];
                
                for (int i = 0; i < results.size(); i++) {
                    com.mapbox.search.result.SearchResult result = results.get(i);
                    JSObject resultObj = new JSObject();
                    
                    resultObj.put("id", result.getId());
                    resultObj.put("name", result.getName());
                    resultObj.put("address", result.getAddress() != null ? result.getAddress().toString() : "");
                    
                    JSObject coordinate = new JSObject();
                    com.mapbox.geojson.Point point = result.getCoordinate();
                    coordinate.put("latitude", point.latitude());
                    coordinate.put("longitude", point.longitude());
                    resultObj.put("coordinate", coordinate);
                    
                    if (result.getCategories() != null && !result.getCategories().isEmpty()) {
                        resultObj.put("categories", result.getCategories());
                    } else {
                        resultObj.put("categories", new String[]{});
                    }
                    
                    geocodeResults[i] = resultObj;
                }
                
                ret.put("results", geocodeResults);
                call.resolve(ret);
            }

            @Override
            public void onError(String error) {
                call.reject(error);
            }
        });
    }

    @PluginMethod
    public void searchMapboxByCategory(PluginCall call) {
        String category = call.getString("category");
        Double latitude = call.getDouble("latitude");
        Double longitude = call.getDouble("longitude");
        
        if (category == null || category.isEmpty()) {
            call.reject("Category parameter is required");
            return;
        }
        
        if (latitude == null || longitude == null) {
            call.reject("Latitude and longitude parameters are required");
            return;
        }

        getMapboxSearchImplementation().searchByCategory(category, latitude, longitude, new CapacitorMapboxSearch.SearchResultsCallback() {
            @Override
            public void onResults(List<com.mapbox.search.result.SearchResult> results) {
                JSObject ret = new JSObject();
                JSObject[] categoryResults = new JSObject[results.size()];
                
                for (int i = 0; i < results.size(); i++) {
                    com.mapbox.search.result.SearchResult result = results.get(i);
                    JSObject resultObj = new JSObject();
                    
                    resultObj.put("id", result.getId());
                    resultObj.put("name", result.getName());
                    resultObj.put("address", result.getAddress() != null ? result.getAddress().toString() : "");
                    
                    JSObject coordinate = new JSObject();
                    com.mapbox.geojson.Point point = result.getCoordinate();
                    coordinate.put("latitude", point.latitude());
                    coordinate.put("longitude", point.longitude());
                    resultObj.put("coordinate", coordinate);
                    
                    if (result.getCategories() != null && !result.getCategories().isEmpty()) {
                        resultObj.put("categories", result.getCategories());
                    } else {
                        resultObj.put("categories", new String[]{});
                    }
                    
                    categoryResults[i] = resultObj;
                }
                
                ret.put("results", categoryResults);
                call.resolve(ret);
            }

            @Override
            public void onError(String error) {
                call.reject(error);
            }
        });
    }

    @PluginMethod
    public void validateMapboxToken(PluginCall call) {
        boolean isValid = getMapboxSearchImplementation().validateMapboxToken();
        JSObject ret = new JSObject();
        ret.put("isValid", isValid);
        call.resolve(ret);
    }

    // openMapboxMap method has been removed as it was deprecated

    @PluginMethod
    public void openMapboxSearchBox(PluginCall call) {
        try {
            Intent intent = new Intent(getActivity(), MapboxSearchBoxActivity.class);
            startActivityForResult(call, intent, "handleSearchBoxResult");
        } catch (Exception e) {
            call.reject("Failed to open mapbox search box: " + e.getMessage(), e);
        }
    }

    @ActivityCallback
    private void handleSearchBoxResult(PluginCall call, ActivityResult result) {
        if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
            Intent data = result.getData();
            JSObject ret = new JSObject();
            
            String placeName = data.getStringExtra(MapboxSearchBoxActivity.RESULT_EXTRA_PLACE_NAME);
            String address = data.getStringExtra(MapboxSearchBoxActivity.RESULT_EXTRA_ADDRESS);
            double latitude = data.getDoubleExtra(MapboxSearchBoxActivity.RESULT_EXTRA_LATITUDE, 0.0);
            double longitude = data.getDoubleExtra(MapboxSearchBoxActivity.RESULT_EXTRA_LONGITUDE, 0.0);
            
            ret.put("placeName", placeName != null ? placeName : "");
            ret.put("address", address != null ? address : "");
            ret.put("latitude", latitude);
            ret.put("longitude", longitude);
            ret.put("cancelled", false);
            
            call.resolve(ret);
        } else {
            JSObject ret = new JSObject();
            ret.put("cancelled", true);
            call.resolve(ret);
        }
    }

    @PluginMethod
    public void openMapboxAutocomplete(PluginCall call) {
        try {
            Intent intent = new Intent(getActivity(), MapboxPlaceAutocompleteActivity.class);
            startActivityForResult(call, intent, "handleAutocompleteResult");
        } catch (Exception e) {
            call.reject("Failed to open mapbox autocomplete: " + e.getMessage(), e);
        }
    }

    @ActivityCallback
    private void handleAutocompleteResult(PluginCall call, ActivityResult result) {
        if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
            Intent data = result.getData();
            JSObject ret = new JSObject();

            String placeName = data.getStringExtra(MapboxPlaceAutocompleteActivity.RESULT_EXTRA_PLACE_NAME);
            String address = data.getStringExtra(MapboxPlaceAutocompleteActivity.RESULT_EXTRA_ADDRESS);
            double latitude = data.getDoubleExtra(MapboxPlaceAutocompleteActivity.RESULT_EXTRA_LATITUDE, 0.0);
            double longitude = data.getDoubleExtra(MapboxPlaceAutocompleteActivity.RESULT_EXTRA_LONGITUDE, 0.0);
            String action = data.getStringExtra("action");

            ret.put("placeName", placeName != null ? placeName : "");
            ret.put("address", address != null ? address : "");
            ret.put("latitude", latitude);
            ret.put("longitude", longitude);
            ret.put("cancelled", false);

            // 检查是否是导航操作
            if ("navigate".equals(action)) {
                // 调用导航功能
                try {
                    // 直接调用导航方法，传递坐标
                    startNavigationToLocation(latitude, longitude, placeName != null ? placeName : "");

                    // 返回地点选择结果，但标记为导航操作
                    ret.put("action", "navigate");
                    call.resolve(ret);
                    return;
                } catch (Exception e) {
                    Log.e("CapacitorKtService", "Failed to start navigation: " + e.getMessage(), e);
                    // 如果导航失败，仍然返回地点选择结果
                }
            }

            call.resolve(ret);
        } else {
            JSObject ret = new JSObject();
            ret.put("cancelled", true);
            call.resolve(ret);
        }
    }

    // =================== 权限回调 ===================

    // 导航权限回调，严格按照原始插件逻辑
    @PermissionCallback
    private void navigationPermissionCallback(PluginCall call) {
        if (getPermissionState("location") == com.getcapacitor.PermissionState.GRANTED) {
            // 保持call活跃状态，与原始插件保持一致
            call.setKeepAlive(true);
            currentNavigationCall = call;
            startNavigationFromCall(call);
        } else {
            // 修正原始插件中的错误信息（原始插件错误地写成"Permission is required to take a picture"）
            call.reject("Permission is required for location access");
        }
    }

    // =================== 原始插件兼容方法 ===================
    
    // 获取当前活跃的导航call（原始插件兼容）
    public PluginCall getCurrentCall() {
        return currentNavigationCall;
    }
    
    // 释放当前call（原始插件兼容）
    public void releaseCurrentCall() {
        currentNavigationCall = null;
    }
    
    // 路线进度事件触发（原始插件兼容）
    public void triggerRouteProgressEvent(JSObject data) {
        if (hasListeners("onRouteProgressChange")) {
            notifyListeners("onRouteProgressChange", data);
        }
    }
    
    // 屏幕镜像事件触发（原始插件兼容）
    public void triggerScreenMirroringEvent(JSObject data) {
        if (hasListeners("onScreenMirroringChange")) {
            notifyListeners("onScreenMirroringChange", data);
        }
    }
    
    // 加号按钮点击事件触发（原始插件兼容）
    public void triggerPlusButtonClicked(JSObject data) {
        if (hasListeners("plusButtonClicked")) {
            notifyListeners("plusButtonClicked", data);
        }
    }
    
    // 减号按钮点击事件触发（原始插件兼容）
    public void triggerMinusButtonClicked(JSObject data) {
        if (hasListeners("minusButtonClicked")) {
            notifyListeners("minusButtonClicked", data);
        }
    }
    
    // 导航停止事件触发（原始插件兼容）
    public void triggerOnNavigationStopEvent(JSObject data) {
        if (hasListeners("onNavigationStop")) {
            notifyListeners("onNavigationStop", data);
        }
    }
    
    // 导航完成事件触发（原始插件兼容）
    public void triggerOnNavigationCompleteEvent() {
        if (hasListeners("onNavigationComplete")) {
            notifyListeners("onNavigationComplete", null);
        }
    }

    // =================== Activity回调 ===================

    @ActivityCallback
    private void batteryOptimizationResult(PluginCall call, ActivityResult result) {
        call.resolve();
    }

    // =================== 私有辅助方法 ===================

    private void notifyBluetoothServiceStateChanged(boolean isRunning) {
        JSObject ret = new JSObject();
        ret.put("isRunning", isRunning);
        notifyListeners("bluetoothServiceStateChanged", ret);
    }

    private boolean isAggressivePowerManagementDevice() {
        String manufacturer = Build.MANUFACTURER.toLowerCase();
        return manufacturer.contains("xiaomi") || 
               manufacturer.contains("huawei") || 
               manufacturer.contains("oppo") || 
               manufacturer.contains("vivo") || 
               manufacturer.contains("oneplus") ||
               manufacturer.contains("honor");
    }

    private void openAutoStartSettingsForManufacturer() {
        try {
            String manufacturer = Build.MANUFACTURER.toLowerCase();
            Intent intent = null;
            
            if (manufacturer.contains("xiaomi")) {
                intent = new Intent("miui.intent.action.APP_PERM_EDITOR");
                intent.setClassName("com.miui.securitycenter", "com.miui.permcenter.autostart.AutoStartManagementActivity");
                intent.putExtra("extra_pkgname", getContext().getPackageName());
            } else if (manufacturer.contains("huawei")) {
                intent = new Intent();
                intent.setClassName("com.huawei.systemmanager", "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity");
            } else if (manufacturer.contains("oppo")) {
                intent = new Intent();
                intent.setClassName("com.coloros.safecenter", "com.coloros.safecenter.permission.startup.StartupAppListActivity");
            } else if (manufacturer.contains("vivo")) {
                intent = new Intent();
                intent.setClassName("com.vivo.permissionmanager", "com.vivo.permissionmanager.activity.BgStartUpManagerActivity");
            } else if (manufacturer.contains("oneplus")) {
                intent = new Intent();
                intent.setClassName("com.oneplus.security", "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity");
            }
            
            if (intent != null) {
                getActivity().startActivity(intent);
            } else {
                // fallback to general settings
                Intent settingsIntent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                settingsIntent.setData(Uri.parse("package:" + getContext().getPackageName()));
                getActivity().startActivity(settingsIntent);
            }
        } catch (Exception e) {
            // 如果无法打开特定设置，打开应用详情页面
            Intent settingsIntent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            settingsIntent.setData(Uri.parse("package:" + getContext().getPackageName()));
            getActivity().startActivity(settingsIntent);
        }
    }

    // 严格按照原始插件逻辑从call中提取参数并启动导航
    private void startNavigationFromCall(PluginCall call) {
        try {
            JSArray routes = call.getArray("routes");
            
            if (routes != null && routes.length() > 0) {
                // 修正：使用getJSONObject而不是getJSObject（严格按照原始插件）
                JSONObject toLocation = routes.getJSONObject(0);
                double toLat = toLocation.getDouble("latitude");
                double toLng = toLocation.getDouble("longitude");
                
                // 获取额外参数（扩展功能）
                Boolean simulating = call.getBoolean("simulating", false);
                
                // 获取Activity并在UI线程中显示DialogFragment（严格按照原始插件）
                android.app.Activity activity = getActivity();
                if (activity != null) {
                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                // 严格按照原始插件的方式创建和显示DialogFragment
                                NavigationDialogFragment dialogFragment = new NavigationDialogFragment();
                                Bundle args = new Bundle();
                                args.putDouble("toLat", toLat);
                                args.putDouble("toLng", toLng);
                                // 传递额外参数（扩展功能，原始插件没有）
                                args.putBoolean("simulating", simulating);
                                args.putString("routes", routes.toString());
                                dialogFragment.setArguments(args);
                                
                                // 使用supportFragmentManager显示DialogFragment
                                dialogFragment.show(((androidx.fragment.app.FragmentActivity) activity).getSupportFragmentManager(), "NavigationDialogFragment");
                            } catch (Exception e) {
                                if (currentNavigationCall != null) {
                                    currentNavigationCall.reject("Failed to show navigation dialog: " + e.getMessage(), e);
                                }
                            }
                        }
                    });
                } else {
                    call.reject("Activity not available");
                }
            } else {
                call.reject("Invalid routes data");
            }
        } catch (Exception e) {
            call.reject("Failed to start navigation: " + e.getMessage(), e);
        }
    }

    // 直接启动导航到指定位置的简化方法
    private void startNavigationToLocation(double latitude, double longitude, String placeName) {
        try {
            // 检查权限
            if (getPermissionState("location") != com.getcapacitor.PermissionState.GRANTED) {
                Log.w("CapacitorKtService", "Location permission not granted, cannot start navigation");
                return;
            }

            // 获取Activity并在UI线程中显示DialogFragment
            android.app.Activity activity = getActivity();
            if (activity != null) {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            // 创建导航对话框
                            NavigationDialogFragment dialogFragment = new NavigationDialogFragment();
                            Bundle args = new Bundle();
                            args.putDouble("toLat", latitude);
                            args.putDouble("toLng", longitude);
                            args.putBoolean("simulating", false);

                            // 构建简单的路由数据
                            JSArray routes = new JSArray();
                            JSObject route = new JSObject();
                            route.put("latitude", latitude);
                            route.put("longitude", longitude);
                            route.put("name", placeName);
                            routes.put(route);

                            args.putString("routes", routes.toString());
                            dialogFragment.setArguments(args);

                            // 显示导航对话框
                            dialogFragment.show(((androidx.fragment.app.FragmentActivity) activity).getSupportFragmentManager(), "NavigationDialogFragment");

                            Log.i("CapacitorKtService", "Navigation started to: " + placeName + " (" + latitude + ", " + longitude + ")");
                        } catch (Exception e) {
                            Log.e("CapacitorKtService", "Failed to show navigation dialog: " + e.getMessage(), e);
                        }
                    }
                });
            } else {
                Log.e("CapacitorKtService", "Activity not available for navigation");
            }
        } catch (Exception e) {
            Log.e("CapacitorKtService", "Failed to start navigation: " + e.getMessage(), e);
        }
    }
}