import { ref, computed, readonly } from 'vue'
import { isPlatform } from '@ionic/vue'
import { useBluetoothData } from './useBluetoothData'

/**
 * 蓝牙发送管理 Composable
 * 使用 Vue 3 最佳实践：响应式状态 + 平台适配
 * 
 * 这个 composable 负责：
 * 1. 平台检测和适配
 * 2. 蓝牙数据发送
 * 3. 发送状态管理
 * 
 * 🔧 修复循环依赖：不再直接依赖 useSmartBluetoothMessage
 */
export function useBluetoothSender() {
  // 获取蓝牙数据
  const { currentSendData } = useBluetoothData()

  // 发送状态
  const isWebSending = ref(false)
  const isNativeSending = ref(false)
  const lastSendTime = ref(0)
  const sendCount = ref(0)

  // 平台检测
  const isAndroid = computed(() => isPlatform('android'))
  const currentMethod = computed(() => isAndroid.value ? 'native' : 'webview')
  
  // 统一的发送状态
  const isSending = computed(() => {
    return isAndroid.value ? isNativeSending.value : isWebSending.value
  })

  // 🔧 延迟加载平台特定的钩子函数，避免循环依赖
  let platformHook: any = null
  
  const getPlatformHook = async () => {
    if (platformHook) return platformHook
    
    if (isAndroid.value) {
      // 🔧 优先复用现有的原生蓝牙单例
      const { getSingleton } = await import('@/utils/bluetoothSingleton')
      platformHook = getSingleton('useNativeBluetoothMessage')
      
      if (!platformHook) {
        // 如果不存在，才创建新的
        const { useNativeBluetoothMessage } = await import('@/hooks/useNativeBluetoothMessage')
        platformHook = useNativeBluetoothMessage()
      }
    } else {
      // 动态导入WebView蓝牙钩子
      const { useMessage } = await import('@/hooks/useMessage')
      platformHook = useMessage()
    }
    
    return platformHook
  }
  
  // 发送数据方法（统一处理）
  const sendData = async (): Promise<boolean> => {
    try {
      console.log('📤 发送数据:', currentSendData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))

      const hook = await getPlatformHook()
      
      // 根据平台调用不同的发送方法
      let success = false
      if (isAndroid.value && hook.updateNativeBluetoothData) {
        // Android 原生方案：更新数据
        await hook.updateNativeBluetoothData()
        success = true
      } else if (!isAndroid.value && hook.sendSingleMessage) {
        // WebView 方案：发送单次消息
        success = await hook.sendSingleMessage()
      }

      if (success) {
        lastSendTime.value = Date.now()
        sendCount.value++
        return true
      }

      return false
    } catch (error) {
      console.error('❌ 发送数据失败:', error)
      return false
    }
  }
  
  // 启动发送服务
  const startSending = async (): Promise<boolean> => {
    try {
      console.log(`🚀 启动 ${currentMethod.value} 发送服务`)

      const hook = await getPlatformHook()
      
      let success = false
      if (isAndroid.value && hook.startNativeBluetoothSending) {
        // Android 原生方案
        success = await hook.startNativeBluetoothSending()
        if (success) {
          isNativeSending.value = true
        }
      } else if (!isAndroid.value && hook.sendMessage) {
        // WebView 方案
        success = await hook.sendMessage()
        if (success) {
          isWebSending.value = true
        }
      }

      return success
    } catch (error) {
      console.error('❌ 启动发送服务失败:', error)
      return false
    }
  }
  
  // 停止发送服务
  const stopSending = async (): Promise<boolean> => {
    try {
      console.log(`⏹️ 停止 ${currentMethod.value} 发送服务`)

      const hook = await getPlatformHook()
      
      let success = false
      if (isAndroid.value && hook.stopNativeBluetoothSending) {
        // Android 原生方案
        await hook.stopNativeBluetoothSending()
        isNativeSending.value = false
        success = true
      } else if (!isAndroid.value && hook.stopSendMessage) {
        // WebView 方案
        await hook.stopSendMessage()
        isWebSending.value = false
        success = true
      }

      return success
    } catch (error) {
      console.error('❌ 停止发送服务失败:', error)
      return false
    }
  }
  
  // 获取发送状态
  const getSendingStatus = () => {
    return {
      isSending: isSending.value,
      method: currentMethod.value,
      lastSendTime: lastSendTime.value,
      sendCount: sendCount.value,
      isAndroid: isAndroid.value
    }
  }
  
  return {
    // 状态
    isSending,
    isWebSending: readonly(isWebSending),
    isNativeSending: readonly(isNativeSending),
    currentMethod,
    isAndroid,
    lastSendTime: readonly(lastSendTime),
    sendCount: readonly(sendCount),
    
    // 方法
    sendData,
    startSending,
    stopSending,
    getSendingStatus
  }
}
