package com.kunteng.plugins.kt;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;
import java.util.UUID;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

public class NativeBluetoothManager {
    private static final String TAG = "NativeBluetoothManager";
    
    // 蓝牙相关
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothGatt bluetoothGatt;
    private BluetoothGattCharacteristic targetCharacteristic;
    private Context context;
    
    // 连接参数
    private String deviceId;
    private UUID serviceUUID;
    private UUID characteristicUUID;
    
    // 发送参数
    private int[] sendData;
    private volatile long dataVersion = 0; // 数据版本号，用于确保使用最新数据
    private volatile long lastUpdateTime = 0; // 最后更新时间
    private int sendInterval = 106; // 默认106ms
    private AtomicBoolean isSending = new AtomicBoolean(false);
    private AtomicBoolean isConnected = new AtomicBoolean(false);
    private AtomicBoolean isWriting = new AtomicBoolean(false); // 🔧 添加写入状态锁，防止并发写入
    
    // 统计信息
    private AtomicLong totalSent = new AtomicLong(0);
    private AtomicLong successCount = new AtomicLong(0);
    private AtomicLong errorCount = new AtomicLong(0);
    private AtomicLong lastSendTime = new AtomicLong(0);
    private String lastError = null;
    
    // 定时器相关
    private ScheduledExecutorService sendExecutor;
    private ScheduledFuture<?> sendTask;
    private HandlerThread bluetoothThread;
    private Handler bluetoothHandler;

    // 🎯 高精度定时器相关
    private HandlerThread precisionTimerThread;
    private Handler precisionTimerHandler;
    private volatile boolean isPrecisionTimerRunning = false;
    private volatile long lastPrecisionSendTime = 0;
    private volatile long precisionSendCount = 0;

    // 🔧 连接和发送状态分离
    private volatile boolean shouldStartSendingAfterConnection = false;
    
    // 🔧 强化的连接重试机制
    private static final int MAX_RETRY_COUNT = Integer.MAX_VALUE; // 🔧 无限重试，确保绝对稳定
    private int retryCount = 0;
    private Handler retryHandler;
    
    // 🔧 连接健康监控
    private static final long CONNECTION_HEALTH_CHECK_INTERVAL = 5000; // 5秒检查一次
    private Handler healthCheckHandler;
    private Runnable healthCheckRunnable;
    private volatile long lastSuccessfulSendTime = 0;
    private volatile boolean forceReconnectRequested = false;
    
    // 🔧 连接状态锁，防止并发连接
    private final AtomicBoolean isConnecting = new AtomicBoolean(false);
    
    // 回调接口
    public interface BluetoothCallback {
        void onConnectionStateChanged(boolean connected);
        void onDataReceived(int[] data);
        void onSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount);
        void onError(String error, int errorCode);
    }
    
    private BluetoothCallback callback;
    
    public NativeBluetoothManager(Context context) {
        this.context = context;
        this.bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        this.retryHandler = new Handler(Looper.getMainLooper());

        // 创建专用的蓝牙线程
        bluetoothThread = new HandlerThread("BluetoothThread");
        bluetoothThread.start();
        bluetoothHandler = new Handler(bluetoothThread.getLooper());

        // 🎯 创建高精度定时器线程
        precisionTimerThread = new HandlerThread("PrecisionTimerThread");
        precisionTimerThread.setPriority(Thread.MAX_PRIORITY); // 设置最高优先级
        precisionTimerThread.start();
        precisionTimerHandler = new Handler(precisionTimerThread.getLooper());

        // 🔧 创建连接健康监控处理器
        healthCheckHandler = new Handler(Looper.getMainLooper());
        
        Log.d(TAG, "NativeBluetoothManager initialized with precision timer and health monitoring");
    }
    
    public void setCallback(BluetoothCallback callback) {
        this.callback = callback;
    }
    
    // 🔧 准备蓝牙发送服务（不立即连接）
    public boolean prepareBluetoothSending(String deviceId, String serviceUUID, String characteristicUUID,
                                         int sendInterval, int[] data) {
        try {
            this.deviceId = deviceId;
            this.serviceUUID = UUID.fromString(serviceUUID);
            this.characteristicUUID = UUID.fromString(characteristicUUID);
            this.sendInterval = sendInterval;
            this.sendData = data.clone();

            Log.d(TAG, "🔧 准备蓝牙发送服务 - Device: " + deviceId + ", Interval: " + sendInterval + "ms");

            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                Log.e(TAG, "Bluetooth adapter not available or disabled");
                notifyError("蓝牙适配器不可用或未启用", -1);
                return false;
            }

            // 如果已经在发送，先停止
            if (isSending.get()) {
                stopBluetoothSending();
            }

            // 🔧 标记需要在连接后启动发送
            shouldStartSendingAfterConnection = true;

            Log.d(TAG, "✅ 蓝牙发送服务已准备，等待连接成功后启动");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "Failed to prepare bluetooth sending: " + e.getMessage());
            notifyError("准备蓝牙发送失败: " + e.getMessage(), -2);
            return false;
        }
    }

    // 🔧 启动蓝牙发送服务（兼容旧接口）
    public boolean startBluetoothSending(String deviceId, String serviceUUID, String characteristicUUID,
                                       int sendInterval, int[] data) {
        // 先准备发送服务
        boolean prepared = prepareBluetoothSending(deviceId, serviceUUID, characteristicUUID, sendInterval, data);
        if (!prepared) {
            return false;
        }

        // 如果已经连接，立即启动发送
        if (isConnected.get() && targetCharacteristic != null) {
            Log.d(TAG, "🔧 设备已连接，立即启动发送");
            startDataSending();
            return true;
        }

        // 否则连接设备，连接成功后会自动启动发送
        Log.d(TAG, "🔧 设备未连接，先连接设备");
        connectToDevice();
        return true;
    }
    
    // 停止蓝牙发送服务
    public void stopBluetoothSending() {
        Log.d(TAG, "Stopping bluetooth sending");
        
        isSending.set(false);
        
        // 停止发送定时器
        if (sendTask != null && !sendTask.isCancelled()) {
            sendTask.cancel(true);
            sendTask = null;
        }
        
        if (sendExecutor != null && !sendExecutor.isShutdown()) {
            sendExecutor.shutdown();
            sendExecutor = null;
        }

        // 🎯 停止高精度定时器
        stopPrecisionTimer();

        // 🔧 停止连接健康监控
        stopConnectionHealthMonitoring();

        // 断开蓝牙连接
        disconnectDevice();
        
        // 通知状态变化
        notifySendingStateChanged(false, lastSendTime.get(), totalSent.get(), errorCount.get());
    }
    
    // 更新发送数据
    public synchronized void updateSendData(int[] data) {
        if (data != null) {
            this.sendData = data.clone();
            this.dataVersion++; // 增加版本号
            this.lastUpdateTime = System.currentTimeMillis();
            
            // 🔧 增强调试：详细记录数据更新过程
            StringBuilder dataLog = new StringBuilder("📝 蓝牙数据已更新: [");
            for (int i = 0; i < Math.min(data.length, 10); i++) { // 只显示前10个数据避免日志过长
                dataLog.append(data[i]);
                if (i < Math.min(data.length, 10) - 1) dataLog.append(", ");
            }
            if (data.length > 10) dataLog.append("...");
            dataLog.append("] 长度: ").append(data.length);
            dataLog.append(" 版本: ").append(dataVersion);
            
            Log.d(TAG, dataLog.toString());
            
            // 🔧 记录数据更新时间戳，用于调试
            Log.d(TAG, "🕐 数据更新时间戳: " + lastUpdateTime + " (版本: " + dataVersion + ")");
            
            // 🚀 立即触发一次发送以确保新数据生效
            if (isSending.get() && isConnected.get()) {
                Log.d(TAG, "🚀 数据更新后立即触发发送");
                bluetoothHandler.post(this::sendDataImmediately);
            }
        } else {
            Log.w(TAG, "⚠️ 尝试更新空数据，忽略此次更新");
        }
    }
    
    // 获取当前发送数据
    public int[] getCurrentSendData() {
        return sendData != null ? sendData.clone() : new int[0];
    }
    
    // 立即发送数据（用于数据更新后立即生效）
    private void sendDataImmediately() {
        // 🔧 检查是否正在写入，避免并发
        if (!isWriting.compareAndSet(false, true)) {
            Log.w(TAG, "⚠️ 上一个写入操作未完成，跳过此次发送");
            return;
        }

        try {
            // 严格的状态检查
            if (!isSending.get() || !isConnected.get() ||
                bluetoothGatt == null || targetCharacteristic == null) {
                Log.w(TAG, "⚠️ 发送条件不满足，跳过此次发送");
                return;
            }

            // 准备发送数据 - 使用同步确保数据一致性
            int[] currentSendData;
            long currentVersion;
            long currentUpdateTime;

            synchronized (this) {
                currentSendData = sendData != null ? sendData.clone() : null;
                currentVersion = dataVersion;
                currentUpdateTime = lastUpdateTime;
            }

            if (currentSendData == null || currentSendData.length == 0) {
                Log.w(TAG, "写入中止：发送数据为空");
                return;
            }

            // 🔧 检查特征属性
            int properties = targetCharacteristic.getProperties();
            if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 &&
                (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
                Log.e(TAG, "❌ 特征不支持写入，属性: " + properties);
                return;
            }
            
            // 🔧 修复数据转换问题：确保正确的byte转换
            byte[] dataToSend = new byte[currentSendData.length];
            StringBuilder dataLog = new StringBuilder("📤 发送数据转换: ");
            
            for (int i = 0; i < currentSendData.length; i++) {
                int originalValue = currentSendData[i];
                // 确保值在0-255范围内，然后转换为byte
                int clampedValue = originalValue & 0xFF;
                dataToSend[i] = (byte) clampedValue;
                
                // 记录数据转换过程 - 只显示前10个数据
                if (i < 10) {
                    dataLog.append(String.format("[%d: %d->%02X] ", i, originalValue, dataToSend[i] & 0xFF));
                }
            }
            if (currentSendData.length > 10) dataLog.append("...");
            
            // 🔧 添加发送时间戳和版本信息用于调试
            Log.d(TAG, dataLog.toString());
            Log.d(TAG, "🕐 发送时间戳: " + System.currentTimeMillis() + " (数据版本: " + currentVersion + ", 更新时间: " + currentUpdateTime + ")");
            Log.d(TAG, "发送数据长度: " + dataToSend.length + " 字节");
            
            // 🔍 检查数据长度限制（一般BLE设备最大20字节）
            if (dataToSend.length > 20) {
                Log.w(TAG, "警告：数据长度超过20字节 (" + dataToSend.length + ")，可能导致写入失败");
            }
            
            // 🔧 验证连接状态和特征有效性
            if (targetCharacteristic == null) {
                errorCount.incrementAndGet();
                isWriting.set(false);
                Log.e(TAG, "❌ 无法发送：targetCharacteristic为null");
                return;
            }
            
            if (bluetoothGatt == null) {
                errorCount.incrementAndGet();
                isWriting.set(false);
                Log.e(TAG, "❌ 无法发送：bluetoothGatt为null");
                return;
            }
            
            // 🔧 检查连接状态
            if (!isConnected.get()) {
                errorCount.incrementAndGet();
                isWriting.set(false);
                Log.w(TAG, "⚠️ 设备未连接，跳过发送");
                return;
            }

            // 写入特征
            targetCharacteristic.setValue(dataToSend);
            boolean writeResult = bluetoothGatt.writeCharacteristic(targetCharacteristic);
            
            if (writeResult) {
                totalSent.incrementAndGet();
                lastSendTime.set(System.currentTimeMillis());
                lastSuccessfulSendTime = System.currentTimeMillis(); // 🔧 更新最后成功发送时间
                Log.v(TAG, "写入请求已提交，数据长度: " + dataToSend.length + " (版本: " + currentVersion + ")");
            } else {
                errorCount.incrementAndGet();
                isWriting.set(false); // 🔧 立即释放锁

                lastError = String.format("写入失败 - 连接: %s, GATT: %s, 数据长度: %d",
                    isConnected.get() ? "正常" : "异常",
                    bluetoothGatt != null ? "正常" : "null",
                    dataToSend.length);
                Log.e(TAG, "❌ " + lastError);

                // 🔧 记录错误但不断开连接，给连接恢复的机会
                if (errorCount.get() % 10 == 0) {
                    Log.w(TAG, "🔧 写入失败次数较多，但继续保持连接尝试恢复");
                }
            }

        } catch (Exception e) {
            errorCount.incrementAndGet();
            isWriting.set(false); // 🔧 释放锁
            lastError = "写入异常: " + e.getMessage() + ", 堆栈: " + e.getClass().getSimpleName();
            Log.e(TAG, "❌ " + lastError, e);
        } finally {
            // 🔧 确保在所有情况下都释放写入锁
            isWriting.set(false);
        }
    }
    
    // 连接到蓝牙设备
    private void connectToDevice() {
        bluetoothHandler.post(() -> {
            try {
                // 🔧 防止并发连接
                if (!isConnecting.compareAndSet(false, true)) {
                    Log.w(TAG, "🔧 连接已在进行中，跳过重复连接请求");
                    return;
                }
                
                BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceId);
                if (device == null) {
                    Log.e(TAG, "Device not found: " + deviceId);
                    notifyError("设备未找到: " + deviceId, -3);
                    isConnecting.set(false);
                    return;
                }

                Log.d(TAG, "🔧 开始连接设备: " + device.getName() + " (" + deviceId + ")");
                
                // 🔧 强制清理旧连接，确保完全断开（不通知回调，避免重复通知）
                cleanupBluetoothConnection(false);
                
                // 🔧 使用Handler延迟连接，避免阻塞线程
                bluetoothHandler.postDelayed(() -> {
                    try {
                        // 建立GATT连接
                        bluetoothGatt = device.connectGatt(context, false, gattCallback);
                        Log.d(TAG, "🔧 GATT连接请求已提交");
                    } catch (Exception e) {
                        Log.e(TAG, "🔧 延迟连接失败: " + e.getMessage());
                        isConnecting.set(false);
                        notifyError("延迟连接失败: " + e.getMessage(), -4);
                    }
                }, 200); // 200ms延迟
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to connect to device: " + e.getMessage());
                notifyError("连接设备失败: " + e.getMessage(), -4);
                isConnecting.set(false);
            }
        });
    }
    
    // 断开蓝牙设备
    private void disconnectDevice() {
        bluetoothHandler.post(() -> {
            cleanupBluetoothConnection();
            Log.d(TAG, "Device disconnected");
        });
    }
    
    // 🔧 新增：彻底清理蓝牙连接的方法
    private void cleanupBluetoothConnection() {
        cleanupBluetoothConnection(true);
    }
    
    // 🔧 清理蓝牙连接的重载方法，可选择是否通知状态变化
    private void cleanupBluetoothConnection(boolean notifyCallback) {
        try {
            // 先设置连接状态为false，防止并发操作
            boolean wasConnected = isConnected.getAndSet(false);
            targetCharacteristic = null;
            
            if (bluetoothGatt != null) {
                Log.d(TAG, "🔧 开始清理GATT连接...");
                
                // 先尝试断开连接
                try {
                    bluetoothGatt.disconnect();
                    Log.d(TAG, "🔧 GATT disconnect调用完成");
                } catch (Exception e) {
                    Log.w(TAG, "🔧 GATT disconnect异常: " + e.getMessage());
                }
                
                // 🔧 使用Handler延迟关闭，避免阻塞线程
                bluetoothHandler.postDelayed(() -> {
                    try {
                        if (bluetoothGatt != null) {
                            bluetoothGatt.close();
                            Log.d(TAG, "🔧 GATT close调用完成");
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "🔧 GATT close异常: " + e.getMessage());
                    }
                }, 100); // 100ms延迟
                
                bluetoothGatt = null;
                Log.d(TAG, "🔧 GATT连接清理完成");
            }
            
            // 🔧 只有在需要时才通知连接状态变化，避免重复通知
            if (notifyCallback && wasConnected && callback != null) {
                callback.onConnectionStateChanged(false);
                Log.d(TAG, "🔧 已通知连接状态变化: false");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "🔧 清理蓝牙连接时发生异常: " + e.getMessage(), e);
            // 即使发生异常，也要确保状态重置
            bluetoothGatt = null;
            isConnected.set(false);
            targetCharacteristic = null;
        }
    }
    
    // GATT回调
    private final BluetoothGattCallback gattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            // 🔧 记录详细的连接状态信息用于调试
            Log.d(TAG, String.format("🔧 连接状态变化: status=%d, newState=%d", status, newState));
            
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "🔧 已连接到GATT服务器");
                isConnected.set(true);
                isConnecting.set(false); // 🔧 释放连接锁
                retryCount = 0; // 重置重试计数
                
                // 发现服务
                gatt.discoverServices();
                
                if (callback != null) {
                    callback.onConnectionStateChanged(true);
                }
                
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                Log.d(TAG, "🔧 已从GATT服务器断开连接");
                isConnected.set(false);
                isConnecting.set(false); // 🔧 释放连接锁
                
                if (callback != null) {
                    callback.onConnectionStateChanged(false);
                }
                
                // 🔧 检查断开原因，如果是连接失败则不立即重连
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    Log.w(TAG, String.format("🔧 连接状态异常: status=%d, 延迟重连", status));
                }
                
                // 🔧 如果正在发送且意外断开，无限重连确保绝对稳定
                if (isSending.get()) {
                    retryCount++;
                    Log.w(TAG, "🔧 连接丢失，开始无限重连 (第" + retryCount + "次尝试)");
                    
                    // 🔧 根据错误状态和重试次数调整重连延迟
                    long baseDelay = status != BluetoothGatt.GATT_SUCCESS ? 3000 : 2000;
                    // 🔧 使用指数退避，但有上限，避免等待时间过长
                    long exponentialDelay = Math.min(baseDelay * (long)Math.pow(1.5, Math.min(retryCount - 1, 5)), 10000);
                    
                    Log.d(TAG, "🔧 重连延迟: " + exponentialDelay + "ms");
                    
                    retryHandler.postDelayed(() -> {
                        if (isSending.get()) {
                            Log.d(TAG, "🔧 开始自动重连 (第" + retryCount + "次尝试)...");
                            connectToDevice();
                        } else {
                            Log.d(TAG, "🔧 发送已停止，取消重连");
                        }
                    }, exponentialDelay);
                } else {
                    Log.d(TAG, "🔧 发送未启动，不进行重连");
                }
            } else if (newState == BluetoothProfile.STATE_CONNECTING) {
                Log.d(TAG, "🔧 正在连接到GATT服务器...");
                // 🔧 确保连接状态锁已设置
                isConnecting.set(true);
            } else if (newState == BluetoothProfile.STATE_DISCONNECTING) {
                Log.d(TAG, "🔧 正在从GATT服务器断开连接...");
                // 🔧 在断开过程中设置连接状态为false
                isConnected.set(false);
            } else {
                Log.w(TAG, "🔧 未知连接状态: " + newState);
                // 🔧 对于未知状态，释放连接锁
                isConnecting.set(false);
            }
        }
        
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "Services discovered");

                // 查找目标服务和特征
                BluetoothGattService service = gatt.getService(serviceUUID);
                if (service != null) {
                    targetCharacteristic = service.getCharacteristic(characteristicUUID);
                    if (targetCharacteristic != null) {
                        Log.d(TAG, "🎯 目标特征已找到");

                        // 🔧 启用通知（如果支持）
                        if ((targetCharacteristic.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                            boolean notificationResult = gatt.setCharacteristicNotification(targetCharacteristic, true);
                            Log.d(TAG, "🔧 通知设置结果: " + notificationResult);
                            
                            // 🔧 如果特征支持通知，还需要写入客户端配置描述符
                            android.bluetooth.BluetoothGattDescriptor descriptor = targetCharacteristic.getDescriptor(
                                java.util.UUID.fromString("00002902-0000-1000-8000-00805f9b34fb"));
                            if (descriptor != null) {
                                descriptor.setValue(android.bluetooth.BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
                                boolean descriptorResult = gatt.writeDescriptor(descriptor);
                                Log.d(TAG, "🔧 通知描述符写入结果: " + descriptorResult);
                            }
                        }

                        // 🔧 只有在需要时才启动发送
                        if (shouldStartSendingAfterConnection) {
                            Log.d(TAG, "🚀 连接成功，启动数据发送");
                            startDataSending();
                            shouldStartSendingAfterConnection = false; // 重置标志
                        } else {
                            Log.d(TAG, "✅ 连接成功，等待发送指令");
                        }
                    } else {
                        Log.e(TAG, "Target characteristic not found");
                        notifyError("目标特征未找到", -6);
                    }
                } else {
                    Log.e(TAG, "Target service not found");
                    notifyError("目标服务未找到", -7);
                }
            } else {
                Log.e(TAG, "Service discovery failed with status: " + status);
                notifyError("服务发现失败", status);
            }
        }
        
        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            isWriting.set(false); // 🔧 释放写入锁

            if (status == BluetoothGatt.GATT_SUCCESS) {
                successCount.incrementAndGet();
                lastSendTime.set(System.currentTimeMillis());
                lastSuccessfulSendTime = System.currentTimeMillis(); // 🔧 更新最后成功发送时间
                Log.v(TAG, "✅ 写入成功，总成功: " + successCount.get());

                // 🔧 记录连续成功但保持106ms间隔不变
                if (successCount.get() % 50 == 0) {
                    Log.d(TAG, "✅ 连续成功，保持106ms发送间隔");
                }
            } else {
                errorCount.incrementAndGet();

                // 🔧 详细的错误状态分析
                String statusDescription = parseGattStatus(status);
                lastError = "写入回调失败: " + statusDescription;

                Log.e(TAG, String.format("❌ 写入失败 - %s (错误率: %.1f%%)",
                    statusDescription,
                    (double) errorCount.get() / (totalSent.get() + 1) * 100));

                // 🔧 针对特定错误的处理 - 但不立即断开连接
                if (status == BluetoothGatt.GATT_CONNECTION_CONGESTED) {
                    Log.w(TAG, "🚧 连接拥塞，但继续保持连接，等待缓解");
                } else if (status == BluetoothGatt.GATT_WRITE_NOT_PERMITTED) {
                    Log.w(TAG, "💡 写入权限问题，但保持连接尝试恢复");
                } else if (status == BluetoothGatt.GATT_INVALID_ATTRIBUTE_LENGTH) {
                    Log.w(TAG, "💡 数据长度问题: " + (sendData != null ? sendData.length : "null") + "，但保持连接");
                }

                // 🔧 只有在连续失败很多次时才考虑重连，而不是立即断开
                long totalAttempts = totalSent.get();
                if (totalAttempts > 20) {
                    double errorRate = (double) errorCount.get() / totalAttempts;
                    if (errorRate > 0.8) { // 错误率超过80%
                        Log.w(TAG, "⚠️ 错误率过高 (" + String.format("%.1f%%", errorRate * 100) + 
                              ")，但保持连接继续尝试");
                    }
                }
            }
            
            totalSent.incrementAndGet();
        }
        
        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            // 处理接收到的数据
            byte[] data = characteristic.getValue();
            if (data != null && data.length > 0) {
                int[] intData = new int[data.length];
                for (int i = 0; i < data.length; i++) {
                    intData[i] = data[i] & 0xFF; // 转换为无符号整数
                }
                
                Log.d(TAG, "Data received, length: " + data.length);
                
                if (callback != null) {
                    callback.onDataReceived(intData);
                }
            }
        }
    };
    
    // 🔧 在连接成功后启动发送（公共方法）
    public boolean startSendingAfterConnection() {
        if (!isConnected.get()) {
            Log.w(TAG, "⚠️ 设备未连接，无法启动发送");
            return false;
        }

        if (targetCharacteristic == null) {
            Log.w(TAG, "⚠️ 目标特征未找到，无法启动发送");
            return false;
        }

        if (sendData == null || sendData.length == 0) {
            Log.w(TAG, "⚠️ 发送数据为空，无法启动发送");
            return false;
        }

        Log.d(TAG, "🚀 连接已就绪，启动数据发送");
        startDataSending();
        return true;
    }

    // 开始数据发送
    private void startDataSending() {
        if (isSending.get()) {
            Log.w(TAG, "Data sending already active");
            return;
        }

        isSending.set(true);

        // 🎯 启动高精度106ms定时器
        Log.d(TAG, "🎯 启动严格106ms高精度定时器");
        startPrecisionTimer();

        // 通知发送状态变化
        notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());

        // 🔧 启动连接健康监控
        startConnectionHealthMonitoring();

        Log.d(TAG, "🎯 高精度数据发送已启动，严格106ms间隔");
    }
    
    // 重新连接设备
    public void reconnectDevice() {
        Log.d(TAG, "🔄 开始重新连接设备...");
        
        bluetoothHandler.post(() -> {
            // 🔧 先停止所有发送操作
            boolean wasSending = isSending.get();
            if (wasSending) {
                stopPrecisionTimer();
                Log.d(TAG, "🔧 已暂停发送，准备重连");
            }
            
            // 🔧 彻底清理现有连接
            cleanupBluetoothConnection();
            retryCount = 0;
            
            // 🔧 延迟重连，给足够时间让旧连接完全释放
            retryHandler.postDelayed(() -> {
                if (wasSending || isSending.get()) {
                    Log.d(TAG, "🔄 开始重新建立连接...");
                    connectToDevice();
                } else {
                    Log.d(TAG, "🔄 重连取消：不再需要发送数据");
                }
            }, 1500); // 增加延迟到1.5秒
        });
    }
    
    // 获取发送状态
    public BluetoothSendingStatus getSendingStatus() {
        return new BluetoothSendingStatus(
            isSending.get(),
            lastSendTime.get(),
            totalSent.get(),
            errorCount.get()
        );
    }
    
    // 获取发送统计
    public BluetoothSendingStats getSendingStats() {
        long total = totalSent.get();
        long success = successCount.get();
        long errors = errorCount.get();
        
        // 计算平均间隔（基于成功发送的数据）
        double averageInterval = success > 1 ? 
            (double) (System.currentTimeMillis() - (lastSendTime.get() - (success * sendInterval))) / success : 
            sendInterval;
        
        return new BluetoothSendingStats(
            total,
            success,
            errors,
            averageInterval,
            lastError,
            isConnected.get()
        );
    }

    // 🔍 诊断写入失败的具体原因
    private void diagnosisWriteFailure() {
        StringBuilder diagnosis = new StringBuilder("🔍 写入失败诊断: ");
        
        try {
            // 检查蓝牙适配器状态
            if (bluetoothAdapter == null) {
                diagnosis.append("蓝牙适配器为null; ");
            } else if (!bluetoothAdapter.isEnabled()) {
                diagnosis.append("蓝牙未启用; ");
            }
            
            // 检查GATT连接状态
            if (bluetoothGatt == null) {
                diagnosis.append("GATT连接为null; ");
            }
            
            // 检查特征状态
            if (targetCharacteristic == null) {
                diagnosis.append("目标特征为null; ");
            } else {
                int properties = targetCharacteristic.getProperties();
                diagnosis.append(String.format("特征属性: %d ", properties));
                
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) != 0) {
                    diagnosis.append("(支持写入) ");
                }
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                    diagnosis.append("(支持无响应写入) ");
                }
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 && 
                    (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
                    diagnosis.append("(不支持写入) ");
                }
            }
            
            // 检查数据状态
            if (sendData == null) {
                diagnosis.append("发送数据为null; ");
            } else {
                diagnosis.append(String.format("数据长度: %d; ", sendData.length));
            }
            
            // 检查连接计数器
            diagnosis.append(String.format("重试次数: %d/%d; ", retryCount, MAX_RETRY_COUNT));
            
            Log.w(TAG, diagnosis.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "诊断过程异常: " + e.getMessage(), e);
        }
    }

    // 🔧 增强的错误状态码解析
    private String parseGattStatus(int status) {
        switch (status) {
            case BluetoothGatt.GATT_SUCCESS:
                return "GATT_SUCCESS(0) - 操作成功";
            case BluetoothGatt.GATT_READ_NOT_PERMITTED:
                return "GATT_READ_NOT_PERMITTED(2) - 读取不被允许";
            case BluetoothGatt.GATT_WRITE_NOT_PERMITTED:
                return "GATT_WRITE_NOT_PERMITTED(3) - 写入不被允许";
            case BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION:
                return "GATT_INSUFFICIENT_AUTHENTICATION(5) - 认证不足";
            case BluetoothGatt.GATT_REQUEST_NOT_SUPPORTED:
                return "GATT_REQUEST_NOT_SUPPORTED(6) - 请求不被支持";
            case BluetoothGatt.GATT_INSUFFICIENT_ENCRYPTION:
                return "GATT_INSUFFICIENT_ENCRYPTION(15) - 加密不足";
            case BluetoothGatt.GATT_INVALID_ATTRIBUTE_LENGTH:
                return "GATT_INVALID_ATTRIBUTE_LENGTH(13) - 属性长度无效";
            case BluetoothGatt.GATT_CONNECTION_CONGESTED:
                return "GATT_CONNECTION_CONGESTED(143) - 连接拥塞";
            case BluetoothGatt.GATT_FAILURE:
                return "GATT_FAILURE(257) - 通用失败";
            default:
                return String.format("未知状态码(%d)", status);
        }
    }
    
    // 清理资源
    public void cleanup() {
        Log.d(TAG, "Cleaning up NativeBluetoothManager");

        stopBluetoothSending();

        if (bluetoothThread != null) {
            bluetoothThread.quitSafely();
            bluetoothThread = null;
        }

        // 🎯 清理高精度定时器线程
        if (precisionTimerThread != null) {
            precisionTimerThread.quitSafely();
            precisionTimerThread = null;
            precisionTimerHandler = null;
        }

        if (retryHandler != null) {
            retryHandler.removeCallbacksAndMessages(null);
            retryHandler = null;
        }

        // 🔧 清理连接健康监控
        if (healthCheckHandler != null) {
            healthCheckHandler.removeCallbacksAndMessages(null);
            healthCheckHandler = null;
        }

        Log.d(TAG, "🎯 高精度定时器和健康监控资源已清理");
    }
    
    // 通知方法
    private void notifyError(String error, int errorCode) {
        lastError = error;
        if (callback != null) {
            callback.onError(error, errorCode);
        }
    }
    
    private void notifySendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
        if (callback != null) {
            callback.onSendingStateChanged(active, lastSendTime, sendCount, errorCount);
        }
    }
    
    // 内部数据类
    public static class BluetoothSendingStatus {
        public final boolean isActive;
        public final long lastSendTime;
        public final long sendCount;
        public final long errorCount;
        
        public BluetoothSendingStatus(boolean isActive, long lastSendTime, long sendCount, long errorCount) {
            this.isActive = isActive;
            this.lastSendTime = lastSendTime;
            this.sendCount = sendCount;
            this.errorCount = errorCount;
        }
    }
    
    public static class BluetoothSendingStats {
        public final long totalSent;
        public final long successCount;
        public final long errorCount;
        public final double averageInterval;
        public final String lastError;
        public final boolean isConnected;
        
        public BluetoothSendingStats(long totalSent, long successCount, long errorCount, 
                                   double averageInterval, String lastError, boolean isConnected) {
            this.totalSent = totalSent;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.averageInterval = averageInterval;
            this.lastError = lastError;
            this.isConnected = isConnected;
        }
    }

    // 🎯 =================== 高精度定时器实现 ===================

    /**
     * 启动高精度106ms定时器
     * 使用自适应算法确保严格的106ms间隔
     */
    private void startPrecisionTimer() {
        if (isPrecisionTimerRunning) {
            Log.w(TAG, "🎯 高精度定时器已在运行");
            return;
        }

        isPrecisionTimerRunning = true;
        lastPrecisionSendTime = System.nanoTime();
        precisionSendCount = 0;

        Log.d(TAG, "🎯 启动高精度106ms定时器");

        // 启动自适应精度定时器
        scheduleNextPrecisionSend();
    }

    /**
     * 停止高精度定时器
     */
    private void stopPrecisionTimer() {
        isPrecisionTimerRunning = false;

        if (precisionTimerHandler != null) {
            precisionTimerHandler.removeCallbacksAndMessages(null);
        }

        Log.d(TAG, "🎯 高精度定时器已停止");
    }

    /**
     * 调度下一次精确发送
     * 使用纳秒级精度计算，确保严格106ms间隔
     */
    private void scheduleNextPrecisionSend() {
        if (!isPrecisionTimerRunning || precisionTimerHandler == null) {
            return;
        }

        precisionTimerHandler.post(() -> {
            if (!isPrecisionTimerRunning) return;

            long currentTime = System.nanoTime();
            long targetInterval = 106_000_000L; // 106ms in nanoseconds

            // 计算实际间隔
            long actualInterval = currentTime - lastPrecisionSendTime;

            // 执行发送
            executePrecisionSend(currentTime, actualInterval);

            // 计算下次发送的精确延迟
            long nextSendTime = lastPrecisionSendTime + targetInterval;
            long delay = Math.max(0, (nextSendTime - System.nanoTime()) / 1_000_000L); // 转换为毫秒

            // 调度下次发送
            if (isPrecisionTimerRunning) {
                precisionTimerHandler.postDelayed(this::scheduleNextPrecisionSend, delay);
            }
        });
    }

    /**
     * 执行精确发送
     */
    private void executePrecisionSend(long currentTime, long actualInterval) {
        // 🎯 严格的状态检查
        if (!isSending.get()) {
            Log.w(TAG, "🎯 发送已停止，跳过精确发送");
            return;
        }

        if (!isConnected.get()) {
            Log.w(TAG, "🎯 设备未连接，跳过精确发送");
            return;
        }

        if (bluetoothGatt == null || targetCharacteristic == null) {
            Log.w(TAG, "🎯 蓝牙组件未就绪，跳过精确发送");
            return;
        }

        precisionSendCount++;

        // 记录精确的时间间隔用于调试
        if (precisionSendCount > 1) {
            double intervalMs = actualInterval / 1_000_000.0;
            Log.v(TAG, String.format("🎯 精确间隔: %.3fms (目标: 106ms, 偏差: %.3fms)",
                intervalMs, intervalMs - 106.0));
        }

        lastPrecisionSendTime = currentTime;

        // 🎯 检查特征属性
        int properties = targetCharacteristic.getProperties();
        if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 &&
            (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
            Log.e(TAG, "🎯 特征不支持写入，属性: " + properties);
            return;
        }

        // 执行实际的数据发送
        sendDataImmediately();

        // 定期通知状态变化（每100次发送通知一次）
        if (precisionSendCount % 100 == 0) {
            notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());
        }
    }

    // 🔧 =================== 连接健康监控机制 ===================

    /**
     * 启动连接健康监控
     * 定期检查连接状态，如果发现问题主动重连
     */
    private void startConnectionHealthMonitoring() {
        if (healthCheckRunnable != null) {
            Log.w(TAG, "🔧 连接健康监控已在运行");
            return;
        }

        Log.d(TAG, "🔧 启动连接健康监控");
        lastSuccessfulSendTime = System.currentTimeMillis();

        healthCheckRunnable = new Runnable() {
            @Override
            public void run() {
                performConnectionHealthCheck();
                
                // 🔧 只要发送服务在运行，就继续监控
                if (isSending.get() && healthCheckHandler != null) {
                    healthCheckHandler.postDelayed(this, CONNECTION_HEALTH_CHECK_INTERVAL);
                }
            }
        };

        healthCheckHandler.postDelayed(healthCheckRunnable, CONNECTION_HEALTH_CHECK_INTERVAL);
    }

    /**
     * 停止连接健康监控
     */
    private void stopConnectionHealthMonitoring() {
        if (healthCheckHandler != null && healthCheckRunnable != null) {
            healthCheckHandler.removeCallbacks(healthCheckRunnable);
            healthCheckRunnable = null;
            Log.d(TAG, "🔧 连接健康监控已停止");
        }
    }

    /**
     * 执行连接健康检查
     */
    private void performConnectionHealthCheck() {
        try {
            long currentTime = System.currentTimeMillis();
            long timeSinceLastSuccess = currentTime - lastSuccessfulSendTime;
            
            Log.v(TAG, String.format("🔧 连接健康检查: 距离上次成功发送 %dms", timeSinceLastSuccess));

            // 🔧 如果超过15秒没有成功发送，认为连接可能有问题
            if (timeSinceLastSuccess > 15000) {
                Log.w(TAG, "🔧 检测到连接可能异常，距离上次成功发送已超过15秒");
                
                // 🔧 如果连接状态显示已连接但实际无法发送，强制重连
                if (isConnected.get() && !isConnecting.get()) {
                    Log.w(TAG, "🔧 连接状态异常，主动触发重连");
                    forceReconnectRequested = true;
                    
                    // 🔧 主动断开并重连
                    bluetoothHandler.post(() -> {
                        cleanupBluetoothConnection(false);
                        // 短暂延迟后重连
                        bluetoothHandler.postDelayed(() -> {
                            if (isSending.get()) {
                                Log.d(TAG, "🔧 健康监控触发的重连");
                                connectToDevice();
                            }
                        }, 1000);
                    });
                }
            }
            
            // 🔧 检查连接状态一致性
            if (isSending.get() && !isConnected.get() && !isConnecting.get()) {
                Log.w(TAG, "🔧 检测到发送服务运行但设备未连接，主动重连");
                connectToDevice();
            }
            
            // 🔧 检查是否存在僵死的连接状态
            if (isConnecting.get() && timeSinceLastSuccess > 30000) {
                Log.w(TAG, "🔧 检测到连接状态可能僵死，重置连接状态");
                isConnecting.set(false);
                if (isSending.get()) {
                    connectToDevice();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "🔧 连接健康检查异常: " + e.getMessage(), e);
        }
    }

    /**
     * 强制重连（外部调用接口）
     */
    public void forceReconnect() {
        Log.d(TAG, "🔧 收到强制重连请求");
        forceReconnectRequested = true;
        
        if (isSending.get()) {
            bluetoothHandler.post(() -> {
                cleanupBluetoothConnection(false);
                bluetoothHandler.postDelayed(() -> {
                    if (isSending.get()) {
                        Log.d(TAG, "🔧 执行强制重连");
                        retryCount = 0; // 重置重试计数
                        connectToDevice();
                    }
                }, 500);
            });
        }
    }

    /**
     * 获取连接健康状态
     */
    public boolean isConnectionHealthy() {
        long timeSinceLastSuccess = System.currentTimeMillis() - lastSuccessfulSendTime;
        return isConnected.get() && timeSinceLastSuccess < 10000; // 10秒内有成功发送认为健康
    }
}