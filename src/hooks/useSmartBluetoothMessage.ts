import { isPlatform } from '@ionic/vue';
import { computed, ref } from 'vue';
import { useMessage } from './useMessage';
import { useBluetoothData } from '@/composables/useBluetoothData';
import { createSingleton } from '@/utils/bluetoothSingleton';

/**
 * 智能蓝牙消息Hook - 响应式架构
 * 根据平台自动选择最佳的发送方案：
 * - Android: 使用原生蓝牙发送 (不受WebView限制)
 * - iOS/Web: 使用WebView方案
 */
export function useSmartBluetoothMessage() {
  return createSingleton('useSmartBluetoothMessage', () => {
    console.log("🔧 useSmartBluetoothMessage - 使用响应式架构");

    // 检测当前平台
    const isAndroid = isPlatform('android');

    // 🔧 按需加载：只初始化当前平台需要的hook
    let webViewHook: any = null;
    let nativeHook: any = null;

    // 🔧 延迟初始化，避免循环依赖和require问题
    const initializeHooks = async () => {
      if (isAndroid && !nativeHook) {
        console.log(`🎯 智能蓝牙消息: Android平台，使用 native 方案`);
        try {
          // 🔧 使用动态导入避免循环依赖和require问题
          const { useNativeBluetoothMessage } = await import('./useNativeBluetoothMessage');
          nativeHook = useNativeBluetoothMessage();
        } catch (error) {
          console.error('❌ 导入原生蓝牙模块失败:', error);
        }
      }
    };

    // 立即初始化WebView hook（没有循环依赖问题）
    if (!isAndroid) {
      console.log(`🎯 智能蓝牙消息: iOS/Web平台，使用 webview 方案`);
      webViewHook = useMessage();
    }

  // 当前使用的方案
  const currentMethod = computed(() => isAndroid ? 'native' : 'webview');
  
  // 统一的服务状态管理（确保返回ref对象保持一致性）
  const unifiedIsServiceRunning = computed(() => {
    if (isAndroid && nativeHook) {
      // 原生Hook返回的是ref对象
      return nativeHook.isNativeSending?.value ?? false;
    } else if (!isAndroid && webViewHook) {
      // WebView Hook返回的是ref对象
      return webViewHook.isServiceRunning?.value ?? false;
    }
    return false;
  });

  // 统一的API接口，根据平台自动路由到对应实现
  const instance = {
    // === 主要发送控制方法 ===
    sendMessage: async () => {
      if (isAndroid) {
        await initializeHooks();
        return nativeHook?.startNativeBluetoothSending?.();
      } else {
        return webViewHook?.sendMessage?.();
      }
    },
    stopSendMessage: async () => {
      if (isAndroid) {
        await initializeHooks();
        return nativeHook?.stopNativeBluetoothSending?.();
      } else {
        return webViewHook?.stopSendMessage?.();
      }
    },

    // 🔧 新增：统一的启动方法（别名）
    startSending: async () => {
      if (isAndroid) {
        await initializeHooks();
        return nativeHook?.startNativeBluetoothSending?.();
      } else {
        return webViewHook?.sendMessage?.();
      }
    },
    stopSending: async () => {
      if (isAndroid) {
        await initializeHooks();
        return nativeHook?.stopNativeBluetoothSending?.();
      } else {
        return webViewHook?.stopSendMessage?.();
      }
    },

    // 🔧 新增：单次发送数据方法
    sendSingleData: async () => {
      if (isAndroid) {
        await initializeHooks();
        return nativeHook?.updateNativeBluetoothData?.();
      } else {
        return webViewHook?.sendSingleMessage?.();
      }
    },
    
    // 🔧 新增：统一的数据更新方法
    updateSmartBluetoothData: async () => {
      console.log("🔧 智能蓝牙方案：使用响应式架构更新数据");

      // 🔧 使用 useBluetoothData 替换 useBluetoothManager，避免循环依赖
      const { currentSendData } = useBluetoothData();

      if (isAndroid) {
        await initializeHooks();
        if (nativeHook?.updateNativeBluetoothData) {
          await nativeHook.updateNativeBluetoothData();
        }
      } else if (!isAndroid && webViewHook?.updateWebBluetoothData) {
        await webViewHook.updateWebBluetoothData();
      }

      console.log("✅ 智能蓝牙数据更新完成（响应式）");
      return true;
    },
    
    // === 服务状态检查 ===
    isServiceRunning: unifiedIsServiceRunning,
    
    // === 数据处理方法 (主要来自WebView方案) ===
    getSpeed: webViewHook?.getSpeed,
    getBattery: webViewHook?.getBattery,
    checkError: webViewHook?.checkError,
    getAssistance: webViewHook?.getAssistance,
    getSingleDistance: webViewHook?.getSingleDistance,
    setBLEName: webViewHook?.setBLEName,

    // === 应用控制 ===
    exitApp: isAndroid ? nativeHook?.exitApp : webViewHook?.exitApp,

    // === WebView特有的高级控制方法 ===
    ...(isAndroid || !webViewHook ? {} : {
      manualRestartTimer: webViewHook.manualRestartTimer,
      getTimerStatus: webViewHook.getTimerStatus,
      isTimerActive: webViewHook.isTimerActive,
      getLastSendTime: webViewHook.getLastSendTime,
      getLastSuccessTime: webViewHook.getLastSuccessTime,
      getFailureCount: webViewHook.getFailureCount,
      getRestartCount: webViewHook.getRestartCount,
      updateWebBluetoothData: webViewHook.updateWebBluetoothData, // 🔧 新增：暴露Web端蓝牙数据更新方法
    }),

    // === 原生方案特有的方法 ===
    ...(isAndroid ? {
      isNativeSending: computed(() => nativeHook?.isNativeSending?.value ?? false),
      sendingStats: computed(() => nativeHook?.sendingStats?.value ?? {}),
      updateSendingStats: async () => {
        await initializeHooks();
        return nativeHook?.updateSendingStats?.();
      },
      updateNativeBluetoothData: async () => {
        await initializeHooks();
        return nativeHook?.updateNativeBluetoothData?.();
      },
      reconnectNativeBluetoothDevice: async () => {
        await initializeHooks();
        return nativeHook?.reconnectNativeBluetoothDevice?.();
      },
      checkNativeSendingStatus: async () => {
        await initializeHooks();
        return nativeHook?.checkNativeSendingStatus?.();
      },
    } : {}),
    
    // === 平台信息 ===
    currentMethod,
    isAndroidNative: isAndroid,
    isWebViewMode: !isAndroid,
    
    // === 调试和监控方法 ===
    getPlatformInfo: () => ({
      platform: isPlatform('android') ? 'Android' : isPlatform('ios') ? 'iOS' : 'Web',
      method: currentMethod.value,
      isNativeSupported: isAndroid,
      capabilities: {
        backgroundSending: isAndroid, // 只有Android原生支持真正的后台发送
        preciseTimer: isAndroid,      // 原生方案有更精确的定时器
        systemIntegration: isAndroid, // 原生方案与系统集成更好
      }
    }),
    
    // === 方案切换提示 (仅用于调试) ===
    getSolutionRecommendation: () => {
      if (isAndroid) {
        return {
          current: '原生蓝牙发送方案',
          benefits: [
            '✅ 熄屏后持续发送 (解决5分钟限制)',
            '✅ 精确106ms间隔 (±5ms误差)',
            '✅ 不受WebView限制',
            '✅ 系统级后台保活',
            '✅ 自动重连机制'
          ],
          status: '推荐使用 - 最佳性能'
        };
      } else {
        return {
          current: 'WebView蓝牙发送方案',
          benefits: [
            '✅ 跨平台兼容性好',
            '✅ 开发调试方便',
            '✅ 现有功能完整'
          ],
          limitations: [
            '⚠️  熄屏5分钟后可能停止发送',
            '⚠️  受WebView系统限制影响',
            '⚠️  定时器精度相对较低'
          ],
          status: '当前平台最佳选择'
        };
      }
    }
  };

  return instance;
  }); // 单例工厂函数结束
}