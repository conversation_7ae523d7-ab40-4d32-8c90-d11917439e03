//
//  MapView.swift
//  CapacitorMapboxSearchPlugin
//
//  Created by 董思盼 on 2025/7/2.
//
import MapboxMaps
import UIKit

extension MapView {
    func makeClusterPointAnnotationManager(
        duration: TimeInterval = 0.5
    ) -> PointAnnotationManager {
        let clusterOptions = ClusterOptions()
        let manager = annotations.makePointAnnotationManager(clusterOptions: clusterOptions)

        // 在新版本中，集群点击事件需要通过不同的方式处理
        // 这里我们先返回 manager，具体的点击处理可以在使用时添加
        return manager
    }
}
