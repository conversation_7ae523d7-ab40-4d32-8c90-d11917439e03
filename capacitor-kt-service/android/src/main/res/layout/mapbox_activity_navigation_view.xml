<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
    
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false">

        <!-- Regular content container -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.mapbox.maps.MapView
                android:id="@+id/mapView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Maneuver View -->
            <com.mapbox.navigation.ui.components.maneuver.view.MapboxManeuverView
                android:id="@+id/maneuverView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_marginBottom="4dp"
                android:layout_marginTop="40dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Sound Button -->
            <com.mapbox.navigation.ui.components.voice.view.MapboxSoundButton
                android:id="@+id/soundButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/maneuverView" />

            <!-- Route Overview Button -->
            <com.mapbox.navigation.ui.components.maps.camera.view.MapboxRouteOverviewButton
                android:id="@+id/routeOverview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/soundButton" />

            <!-- Recenter Button -->
            <com.mapbox.navigation.ui.components.maps.camera.view.MapboxRecenterButton
                android:id="@+id/recenter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/routeOverview" />

            <!-- Mirror Control Button -->
            <ImageButton
                android:id="@+id/mirrorButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/mirror_button_background"
                android:src="@drawable/ic_mirror"
                android:visibility="gone"
                android:contentDescription="镜像控制"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recenter" />

            <!-- Hide Navigation Button -->
            <ImageButton
                android:id="@+id/hideNavigationButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/circular_button_background"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:visibility="gone"
                android:contentDescription="隐藏导航界面"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mirrorButton" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- BottomSheet -->
        <com.kunteng.plugins.kt.CustomTripProgressCard
            android:id="@+id/tripProgressCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
